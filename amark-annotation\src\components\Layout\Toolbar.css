.toolbar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 0 16px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  height: 50px;
  overflow-x: auto;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-section:not(:last-child) {
  padding-right: 20px;
  border-right: 1px solid var(--border-color);
}

.tool-group {
  display: flex;
  gap: 4px;
}

.tool-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: none;
  border: 1px solid transparent;
  border-radius: 6px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s;
}

.tool-button:hover {
  background-color: var(--hover-color);
  border-color: var(--border-color);
}

.tool-button.active {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.tool-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.tool-button:disabled:hover {
  background: none;
  border-color: transparent;
}

.style-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.control-group label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.color-picker {
  width: 32px;
  height: 24px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 3px;
}

.line-width-slider {
  width: 80px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.line-width-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--accent-color);
  border-radius: 50%;
  cursor: pointer;
}

.line-width-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--accent-color);
  border-radius: 50%;
  border: none;
  cursor: pointer;
}

.value-display {
  color: var(--text-primary);
  font-weight: 500;
  min-width: 30px;
  text-align: center;
}

.line-style-select {
  padding: 4px 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
}

.line-style-select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .toolbar {
    gap: 12px;
  }
  
  .toolbar-section {
    gap: 8px;
  }
  
  .toolbar-section:not(:last-child) {
    padding-right: 12px;
  }
  
  .style-controls {
    gap: 12px;
  }
  
  .control-group {
    gap: 4px;
  }
  
  .line-width-slider {
    width: 60px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    flex-wrap: wrap;
    height: auto;
    min-height: 50px;
    padding: 8px 16px;
  }
  
  .toolbar-section:not(:last-child) {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
    margin-bottom: 8px;
  }
  
  .style-controls {
    flex-wrap: wrap;
  }
}
