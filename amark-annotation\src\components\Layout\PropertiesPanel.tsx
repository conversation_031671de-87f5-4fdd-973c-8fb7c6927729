import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { updateMarker } from '../../store/slices/projectSlice';
import { Settings, Tag, Palette } from 'lucide-react';
import './PropertiesPanel.css';

const PropertiesPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject, selectedImageId, selectedMarkerIds } = useSelector((state: RootState) => state.project);
  const [activeTab, setActiveTab] = useState<'properties' | 'style' | 'metadata'>('properties');

  if (!currentProject || !selectedImageId || selectedMarkerIds.length === 0) {
    return (
      <div className="properties-panel">
        <div className="panel-header">
          <h3>属性</h3>
        </div>
        <div className="panel-content">
          <p className="empty-state">请选择一个或多个标注</p>
        </div>
      </div>
    );
  }

  const selectedImage = currentProject.images.find(img => img.id === selectedImageId);
  if (!selectedImage) return null;

  const selectedMarkers = selectedImage.markers.filter(m => selectedMarkerIds.includes(m.id));
  const singleMarker = selectedMarkers.length === 1 ? selectedMarkers[0] : null;

  const handlePropertyUpdate = (property: string, value: any) => {
    selectedMarkerIds.forEach(markerId => {
      dispatch(updateMarker({
        markerId,
        updates: { [property]: value }
      }));
    });
  };

  const handleStyleUpdate = (styleProperty: string, value: any) => {
    selectedMarkerIds.forEach(markerId => {
      const marker = selectedImage.markers.find(m => m.id === markerId);
      if (marker) {
        dispatch(updateMarker({
          markerId,
          updates: {
            style: {
              ...marker.style,
              [styleProperty]: value
            }
          }
        }));
      }
    });
  };

  const renderPropertiesTab = () => (
    <div className="tab-content">
      <div className="property-group">
        <label>名称</label>
        <input
          type="text"
          value={singleMarker?.name || ''}
          onChange={(e) => handlePropertyUpdate('name', e.target.value)}
          placeholder={selectedMarkers.length > 1 ? '多个标注' : '输入名称'}
          disabled={selectedMarkers.length > 1}
        />
      </div>

      <div className="property-group">
        <label>类型</label>
        <input
          type="text"
          value={singleMarker?.type || ''}
          readOnly
          placeholder={selectedMarkers.length > 1 ? '多种类型' : ''}
        />
      </div>

      {singleMarker && (
        <div className="property-group">
          <label>坐标</label>
          <div className="coordinates">
            {singleMarker.points.map((point, index) => (
              <div key={index} className="coordinate-pair">
                <span>点 {index + 1}:</span>
                <span>({point.x.toFixed(1)}, {point.y.toFixed(1)})</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="property-group">
        <label>可见性</label>
        <div className="checkbox-group">
          <input
            type="checkbox"
            checked={singleMarker?.visible ?? false}
            onChange={(e) => handlePropertyUpdate('visible', e.target.checked)}
            disabled={selectedMarkers.length > 1}
          />
          <span>显示标注</span>
        </div>
      </div>

      <div className="property-group">
        <label>锁定</label>
        <div className="checkbox-group">
          <input
            type="checkbox"
            checked={singleMarker?.locked ?? false}
            onChange={(e) => handlePropertyUpdate('locked', e.target.checked)}
            disabled={selectedMarkers.length > 1}
          />
          <span>锁定编辑</span>
        </div>
      </div>
    </div>
  );

  const renderStyleTab = () => (
    <div className="tab-content">
      <div className="property-group">
        <label>颜色</label>
        <input
          type="color"
          value={singleMarker?.style.color || '#ff0000'}
          onChange={(e) => handleStyleUpdate('color', e.target.value)}
          className="color-input"
        />
      </div>

      <div className="property-group">
        <label>线宽</label>
        <input
          type="range"
          min="1"
          max="10"
          step="0.5"
          value={singleMarker?.style.lineWidth || 2}
          onChange={(e) => handleStyleUpdate('lineWidth', parseFloat(e.target.value))}
          className="range-input"
        />
        <span className="value-display">{singleMarker?.style.lineWidth || 2}px</span>
      </div>

      <div className="property-group">
        <label>线型</label>
        <select
          value={singleMarker?.style.lineStyle || 'solid'}
          onChange={(e) => handleStyleUpdate('lineStyle', e.target.value)}
          className="select-input"
        >
          <option value="solid">实线</option>
          <option value="dashed">虚线</option>
          <option value="dotted">点线</option>
        </select>
      </div>

      <div className="property-group">
        <label>填充颜色</label>
        <input
          type="color"
          value={singleMarker?.style.fillColor || '#ff000020'}
          onChange={(e) => handleStyleUpdate('fillColor', e.target.value)}
          className="color-input"
        />
      </div>

      <div className="property-group">
        <label>填充透明度</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={singleMarker?.style.fillOpacity || 0.2}
          onChange={(e) => handleStyleUpdate('fillOpacity', parseFloat(e.target.value))}
          className="range-input"
        />
        <span className="value-display">{((singleMarker?.style.fillOpacity || 0.2) * 100).toFixed(0)}%</span>
      </div>
    </div>
  );

  const renderMetadataTab = () => (
    <div className="tab-content">
      <div className="property-group">
        <label>标签</label>
        <div className="tags-container">
          {singleMarker?.tags.map((tag, index) => (
            <span key={index} className="tag">
              {tag}
            </span>
          )) || <span className="empty-tags">暂无标签</span>}
        </div>
      </div>

      {singleMarker && (
        <>
          <div className="property-group">
            <label>创建时间</label>
            <input
              type="text"
              value={new Date(singleMarker.createdAt).toLocaleString()}
              readOnly
            />
          </div>

          <div className="property-group">
            <label>修改时间</label>
            <input
              type="text"
              value={new Date(singleMarker.updatedAt).toLocaleString()}
              readOnly
            />
          </div>
        </>
      )}
    </div>
  );

  return (
    <div className="properties-panel">
      <div className="panel-header">
        <h3>属性</h3>
        <div className="selection-info">
          {selectedMarkers.length} 个选中
        </div>
      </div>

      <div className="panel-tabs">
        <button
          className={`tab-button ${activeTab === 'properties' ? 'active' : ''}`}
          onClick={() => setActiveTab('properties')}
        >
          <Settings size={14} />
          属性
        </button>
        <button
          className={`tab-button ${activeTab === 'style' ? 'active' : ''}`}
          onClick={() => setActiveTab('style')}
        >
          <Palette size={14} />
          样式
        </button>
        <button
          className={`tab-button ${activeTab === 'metadata' ? 'active' : ''}`}
          onClick={() => setActiveTab('metadata')}
        >
          <Tag size={14} />
          元数据
        </button>
      </div>

      <div className="panel-content">
        {activeTab === 'properties' && renderPropertiesTab()}
        {activeTab === 'style' && renderStyleTab()}
        {activeTab === 'metadata' && renderMetadataTab()}
      </div>
    </div>
  );
};

export default PropertiesPanel;
