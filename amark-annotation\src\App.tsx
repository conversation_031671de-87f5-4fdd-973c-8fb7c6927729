import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import './App.css';

// 简化版本的Header组件
const SimpleHeader = () => (
  <div style={{
    height: '60px',
    backgroundColor: '#f5f5f5',
    borderBottom: '1px solid #ddd',
    display: 'flex',
    alignItems: 'center',
    padding: '0 20px'
  }}>
    <h1 style={{ margin: 0, color: '#007acc' }}>AMARK</h1>
    <span style={{ marginLeft: '20px', color: '#666' }}>图像标注软件</span>
  </div>
);

function App() {
  return (
    <Provider store={store}>
      <div className="app">
        <SimpleHeader />
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          gap: '20px'
        }}>
          <h2>欢迎使用 AMARK 图像标注软件</h2>
          <p>应用正在加载中...</p>
          <div style={{
            padding: '20px',
            backgroundColor: '#e3f2fd',
            borderRadius: '8px',
            maxWidth: '600px',
            textAlign: 'center'
          }}>
            <h3>功能特性</h3>
            <ul style={{ textAlign: 'left', lineHeight: '1.6' }}>
              <li>支持多种标注类型：点、线、矩形、圆形、多边形、自由绘制</li>
              <li>样式定制：颜色、线宽、线型、填充</li>
              <li>数据导出：Excel、CSV、JSON格式</li>
              <li>项目管理和图层组织</li>
              <li>明暗主题切换</li>
            </ul>
          </div>
        </div>
      </div>
    </Provider>
  );
}

export default App;
