import React, { useState } from 'react';

// 测试组件
const TestComponent = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial' }}>
      <h1 style={{ color: '#2196f3' }}>AMARK 测试</h1>
      <p>React 组件正常工作！</p>
      <div style={{
        padding: '20px',
        backgroundColor: '#f0f0f0',
        borderRadius: '8px',
        marginTop: '20px'
      }}>
        <h3>状态检查</h3>
        <ul>
          <li>✅ React 渲染正常</li>
          <li>✅ 样式应用正常</li>
          <li>✅ 组件挂载成功</li>
        </ul>
      </div>
    </div>
  );
};

// 标注类型定义
type AnnotationType = 'select' | 'point' | 'line' | 'rectangle' | 'circle' | 'polygon';

interface Annotation {
  id: string;
  type: AnnotationType;
  points: Array<{x: number, y: number}>;
  style: {
    color: string;
    lineWidth: number;
    fillColor: string;
    opacity: number;
  };
  name: string;
  visible: boolean;
  locked: boolean;
}

interface ImageWithAnnotations {
  id: string;
  name: string;
  url: string;
  annotations: Annotation[];
}

// 简化的主应用组件
const MainApp: React.FC = () => {
  const [projectName, setProjectName] = useState('');
  const [currentProject, setCurrentProject] = useState<string | null>(null);
  const [images, setImages] = useState<ImageWithAnnotations[]>([]);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [activeTool, setActiveTool] = useState<AnnotationType>('select');
  const [currentStyle, setCurrentStyle] = useState({
    color: '#ff0000',
    lineWidth: 2,
    fillColor: '#ff000030',
    opacity: 1
  });
  const [selectedAnnotations, setSelectedAnnotations] = useState<string[]>([]);
  const [isDrawing, setIsDrawing] = useState(false);
  const [tempPoints, setTempPoints] = useState<Array<{x: number, y: number}>>([]);

  const handleCreateProject = () => {
    if (projectName.trim()) {
      setCurrentProject(projectName.trim());
      setProjectName('');
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = () => {
        const newImage: ImageWithAnnotations = {
          id: Date.now().toString(),
          name: file.name,
          url: reader.result as string,
          annotations: []
        };
        setImages(prev => [...prev, newImage]);
      };
      reader.readAsDataURL(file);
    }
  };

  // 添加标注
  const addAnnotation = (type: AnnotationType, points: Array<{x: number, y: number}>) => {
    if (images.length === 0) return;

    const newAnnotation: Annotation = {
      id: Date.now().toString(),
      type,
      points,
      style: currentStyle,
      name: `${type}_${Date.now()}`,
      visible: true,
      locked: false
    };

    const updatedImages = [...images];
    updatedImages[selectedImageIndex].annotations.push(newAnnotation);
    setImages(updatedImages);
  };

  // 删除标注
  const deleteAnnotation = (annotationId: string) => {
    const updatedImages = [...images];
    updatedImages[selectedImageIndex].annotations =
      updatedImages[selectedImageIndex].annotations.filter(ann => ann.id !== annotationId);
    setImages(updatedImages);
    setSelectedAnnotations(prev => prev.filter(id => id !== annotationId));
  };

  // 更新标注样式
  const updateAnnotationStyle = (annotationId: string, newStyle: Partial<Annotation['style']>) => {
    const updatedImages = [...images];
    const annotation = updatedImages[selectedImageIndex].annotations.find(ann => ann.id === annotationId);
    if (annotation) {
      annotation.style = { ...annotation.style, ...newStyle };
      setImages(updatedImages);
    }
  };

  // 导出数据
  const exportData = (format: 'json' | 'csv') => {
    if (images.length === 0) return;

    const currentImage = images[selectedImageIndex];
    const exportData = {
      project: currentProject,
      image: currentImage.name,
      annotations: currentImage.annotations.map(ann => ({
        id: ann.id,
        type: ann.type,
        name: ann.name,
        points: ann.points,
        style: ann.style,
        visible: ann.visible,
        locked: ann.locked
      }))
    };

    if (format === 'json') {
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentProject}_annotations.json`;
      a.click();
      URL.revokeObjectURL(url);
    } else if (format === 'csv') {
      const csvContent = [
        'ID,Type,Name,Points,Color,LineWidth,Visible,Locked',
        ...currentImage.annotations.map(ann =>
          `${ann.id},${ann.type},${ann.name},"${ann.points.map(p => `${p.x},${p.y}`).join(';')}",${ann.style.color},${ann.style.lineWidth},${ann.visible},${ann.locked}`
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentProject}_annotations.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        height: '60px',
        backgroundColor: '#2196f3',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px' }}>AMARK</h1>
        <span style={{ marginLeft: '20px', opacity: 0.9 }}>图像标注软件</span>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          backgroundColor: '#f5f5f5',
          borderRight: '1px solid #ddd',
          padding: '20px'
        }}>
          <h3 style={{ marginTop: 0 }}>项目管理</h3>

          {!currentProject ? (
            <div>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  项目名称:
                </label>
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="输入项目名称"
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>
              <button
                onClick={handleCreateProject}
                disabled={!projectName.trim()}
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: projectName.trim() ? '#2196f3' : '#ccc',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }}
              >
                创建项目
              </button>
            </div>
          ) : (
            <div>
              <div style={{
                padding: '15px',
                backgroundColor: '#e8f5e8',
                borderRadius: '6px',
                marginBottom: '20px',
                border: '1px solid #4caf50'
              }}>
                <p style={{ margin: '0 0 5px 0' }}>
                  <strong>当前项目:</strong> {currentProject}
                </p>
                <p style={{ margin: 0, color: '#666' }}>
                  <strong>图像数量:</strong> {images.length}
                </p>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  上传图像:
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                />
              </div>

              {images.length > 0 && (
                <div>
                  <h4>图像列表:</h4>
                  {images.map((image) => (
                    <div key={image.id} style={{
                      padding: '10px',
                      backgroundColor: 'white',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}>
                      <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
                        {image.name}
                      </div>
                      <img
                        src={image.url}
                        alt={image.name}
                        style={{
                          width: '100%',
                          height: '60px',
                          objectFit: 'cover',
                          borderRadius: '4px'
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              <button
                onClick={() => {
                  setCurrentProject(null);
                  setImages([]);
                }}
                style={{
                  width: '100%',
                  padding: '10px',
                  backgroundColor: '#ff5722',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginTop: '20px'
                }}
              >
                重置项目
              </button>
            </div>
          )}
        </div>

        {/* Main Area */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {currentProject && images.length > 0 ? (
            <>
              {/* 工具栏 */}
              <div style={{
                height: '60px',
                backgroundColor: '#f8f9fa',
                borderBottom: '1px solid #ddd',
                display: 'flex',
                alignItems: 'center',
                padding: '0 20px',
                gap: '15px'
              }}>
                <div style={{ display: 'flex', gap: '8px' }}>
                  {(['select', 'point', 'line', 'rectangle', 'circle'] as AnnotationType[]).map(tool => (
                    <button
                      key={tool}
                      onClick={() => setActiveTool(tool)}
                      style={{
                        padding: '8px 12px',
                        backgroundColor: activeTool === tool ? '#2196f3' : 'white',
                        color: activeTool === tool ? 'white' : '#333',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {tool === 'select' ? '选择' :
                       tool === 'point' ? '点' :
                       tool === 'line' ? '线' :
                       tool === 'rectangle' ? '矩形' :
                       tool === 'circle' ? '圆' : tool}
                    </button>
                  ))}
                </div>

                <div style={{ width: '1px', height: '30px', backgroundColor: '#ddd' }} />

                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <label style={{ fontSize: '12px', color: '#666' }}>颜色:</label>
                  <input
                    type="color"
                    value={currentStyle.color}
                    onChange={(e) => setCurrentStyle(prev => ({ ...prev, color: e.target.value }))}
                    style={{ width: '30px', height: '30px', border: 'none', borderRadius: '4px' }}
                  />

                  <label style={{ fontSize: '12px', color: '#666' }}>线宽:</label>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={currentStyle.lineWidth}
                    onChange={(e) => setCurrentStyle(prev => ({ ...prev, lineWidth: parseInt(e.target.value) }))}
                    style={{ width: '80px' }}
                  />
                  <span style={{ fontSize: '12px', color: '#666', minWidth: '20px' }}>
                    {currentStyle.lineWidth}px
                  </span>
                </div>

                <div style={{ width: '1px', height: '30px', backgroundColor: '#ddd' }} />

                <div style={{ display: 'flex', gap: '8px' }}>
                  <button
                    onClick={() => exportData('json')}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#4caf50',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    导出JSON
                  </button>
                  <button
                    onClick={() => exportData('csv')}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#ff9800',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    导出CSV
                  </button>
                </div>
              </div>

              {/* 画布区域 */}
              <div style={{ flex: 1, display: 'flex' }}>
                <AnnotationCanvas
                  image={images[selectedImageIndex]}
                  activeTool={activeTool}
                  currentStyle={currentStyle}
                  onAddAnnotation={addAnnotation}
                  selectedAnnotations={selectedAnnotations}
                  onSelectAnnotations={setSelectedAnnotations}
                />

                {/* 右侧面板 */}
                <div style={{
                  width: '300px',
                  backgroundColor: '#f8f9fa',
                  borderLeft: '1px solid #ddd',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  {/* 图层面板 */}
                  <div style={{ flex: 1, padding: '15px' }}>
                    <h4 style={{ margin: '0 0 15px 0', fontSize: '14px' }}>
                      标注列表 ({images[selectedImageIndex].annotations.length})
                    </h4>
                    <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                      {images[selectedImageIndex].annotations.map(annotation => (
                        <div
                          key={annotation.id}
                          style={{
                            padding: '8px',
                            backgroundColor: selectedAnnotations.includes(annotation.id) ? '#e3f2fd' : 'white',
                            border: '1px solid #ddd',
                            borderRadius: '4px',
                            marginBottom: '6px',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                          onClick={() => {
                            if (selectedAnnotations.includes(annotation.id)) {
                              setSelectedAnnotations(prev => prev.filter(id => id !== annotation.id));
                            } else {
                              setSelectedAnnotations([annotation.id]);
                            }
                          }}
                        >
                          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <span style={{ fontWeight: 'bold' }}>{annotation.name}</span>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteAnnotation(annotation.id);
                              }}
                              style={{
                                background: 'none',
                                border: 'none',
                                color: '#f44336',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              删除
                            </button>
                          </div>
                          <div style={{ color: '#666', marginTop: '4px' }}>
                            类型: {annotation.type} | 点数: {annotation.points.length}
                          </div>
                          <div style={{
                            width: '20px',
                            height: '3px',
                            backgroundColor: annotation.style.color,
                            marginTop: '4px',
                            borderRadius: '2px'
                          }} />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 样式面板 */}
                  {selectedAnnotations.length > 0 && (
                    <div style={{ padding: '15px', borderTop: '1px solid #ddd' }}>
                      <h4 style={{ margin: '0 0 15px 0', fontSize: '14px' }}>样式设置</h4>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                        <div>
                          <label style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '4px' }}>
                            颜色:
                          </label>
                          <input
                            type="color"
                            value={currentStyle.color}
                            onChange={(e) => {
                              selectedAnnotations.forEach(id => {
                                updateAnnotationStyle(id, { color: e.target.value });
                              });
                            }}
                            style={{ width: '100%', height: '30px', border: 'none', borderRadius: '4px' }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '4px' }}>
                            线宽: {currentStyle.lineWidth}px
                          </label>
                          <input
                            type="range"
                            min="1"
                            max="10"
                            value={currentStyle.lineWidth}
                            onChange={(e) => {
                              const lineWidth = parseInt(e.target.value);
                              selectedAnnotations.forEach(id => {
                                updateAnnotationStyle(id, { lineWidth });
                              });
                            }}
                            style={{ width: '100%' }}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </>
          ) : (
            <div style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#fafafa',
              padding: '20px'
            }}>
              <div style={{ textAlign: 'center', color: '#666', maxWidth: '600px' }}>
                <h2>欢迎使用 AMARK 图像标注软件</h2>
                <p style={{ fontSize: '18px', marginBottom: '30px' }}>
                  {!currentProject
                    ? '请先创建项目开始使用'
                    : '请上传图像开始标注'
                  }
                </p>

                <div style={{
                  padding: '30px',
                  backgroundColor: 'white',
                  borderRadius: '10px',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                }}>
                  <h3 style={{ color: '#2196f3', marginBottom: '20px' }}>🎯 功能特性</h3>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '20px',
                    textAlign: 'left'
                  }}>
                    <div>
                      <h4 style={{ color: '#333', marginBottom: '10px' }}>标注工具</h4>
                      <ul style={{ lineHeight: '1.6', color: '#666' }}>
                        <li>点标记</li>
                        <li>线段</li>
                        <li>矩形</li>
                        <li>圆形</li>
                        <li>多边形</li>
                        <li>自由绘制</li>
                      </ul>
                    </div>
                    <div>
                      <h4 style={{ color: '#333', marginBottom: '10px' }}>数据管理</h4>
                      <ul style={{ lineHeight: '1.6', color: '#666' }}>
                        <li>项目管理</li>
                        <li>图层组织</li>
                        <li>Excel导出</li>
                        <li>CSV导出</li>
                        <li>JSON导出</li>
                        <li>样式定制</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 简化的画布组件
const AnnotationCanvas: React.FC<{
  image: ImageWithAnnotations;
  activeTool: AnnotationType;
  currentStyle: any;
  onAddAnnotation: (type: AnnotationType, points: Array<{x: number, y: number}>) => void;
  selectedAnnotations: string[];
  onSelectAnnotations: (ids: string[]) => void;
}> = ({ image, activeTool, currentStyle, onAddAnnotation, selectedAnnotations, onSelectAnnotations }) => {
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState<{x: number, y: number} | null>(null);
  const [currentPoints, setCurrentPoints] = useState<Array<{x: number, y: number}>>([]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (activeTool === 'select') return;

    const rect = e.currentTarget.getBoundingClientRect();
    const point = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    setIsDrawing(true);
    setStartPoint(point);

    if (activeTool === 'point') {
      onAddAnnotation('point', [point]);
      setIsDrawing(false);
    } else if (activeTool === 'line' || activeTool === 'rectangle' || activeTool === 'circle') {
      setCurrentPoints([point]);
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDrawing || !startPoint) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const currentPoint = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    if (activeTool === 'line' || activeTool === 'rectangle' || activeTool === 'circle') {
      setCurrentPoints([startPoint, currentPoint]);
    }
  };

  const handleMouseUp = () => {
    if (!isDrawing || !startPoint) return;

    if (currentPoints.length === 2) {
      onAddAnnotation(activeTool as AnnotationType, currentPoints);
    }

    setIsDrawing(false);
    setStartPoint(null);
    setCurrentPoints([]);
  };

  const renderAnnotation = (annotation: Annotation) => {
    const isSelected = selectedAnnotations.includes(annotation.id);
    const strokeColor = isSelected ? '#00ff00' : annotation.style.color;
    const strokeWidth = annotation.style.lineWidth;

    switch (annotation.type) {
      case 'point':
        if (annotation.points.length > 0) {
          return (
            <circle
              key={annotation.id}
              cx={annotation.points[0].x}
              cy={annotation.points[0].y}
              r="5"
              fill={strokeColor}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              style={{ cursor: 'pointer' }}
              onClick={() => onSelectAnnotations([annotation.id])}
            />
          );
        }
        break;

      case 'line':
        if (annotation.points.length >= 2) {
          return (
            <line
              key={annotation.id}
              x1={annotation.points[0].x}
              y1={annotation.points[0].y}
              x2={annotation.points[1].x}
              y2={annotation.points[1].y}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              style={{ cursor: 'pointer' }}
              onClick={() => onSelectAnnotations([annotation.id])}
            />
          );
        }
        break;

      case 'rectangle':
        if (annotation.points.length >= 2) {
          const x = Math.min(annotation.points[0].x, annotation.points[1].x);
          const y = Math.min(annotation.points[0].y, annotation.points[1].y);
          const width = Math.abs(annotation.points[1].x - annotation.points[0].x);
          const height = Math.abs(annotation.points[1].y - annotation.points[0].y);

          return (
            <rect
              key={annotation.id}
              x={x}
              y={y}
              width={width}
              height={height}
              fill={annotation.style.fillColor}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              style={{ cursor: 'pointer' }}
              onClick={() => onSelectAnnotations([annotation.id])}
            />
          );
        }
        break;

      case 'circle':
        if (annotation.points.length >= 2) {
          const centerX = annotation.points[0].x;
          const centerY = annotation.points[0].y;
          const radius = Math.sqrt(
            Math.pow(annotation.points[1].x - centerX, 2) +
            Math.pow(annotation.points[1].y - centerY, 2)
          );

          return (
            <circle
              key={annotation.id}
              cx={centerX}
              cy={centerY}
              r={radius}
              fill={annotation.style.fillColor}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              style={{ cursor: 'pointer' }}
              onClick={() => onSelectAnnotations([annotation.id])}
            />
          );
        }
        break;
    }
    return null;
  };

  const renderCurrentDrawing = () => {
    if (!isDrawing || currentPoints.length === 0) return null;

    const strokeColor = currentStyle.color;
    const strokeWidth = currentStyle.lineWidth;

    switch (activeTool) {
      case 'line':
        if (currentPoints.length >= 2) {
          return (
            <line
              x1={currentPoints[0].x}
              y1={currentPoints[0].y}
              x2={currentPoints[1].x}
              y2={currentPoints[1].y}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              strokeDasharray="5,5"
            />
          );
        }
        break;

      case 'rectangle':
        if (currentPoints.length >= 2) {
          const x = Math.min(currentPoints[0].x, currentPoints[1].x);
          const y = Math.min(currentPoints[0].y, currentPoints[1].y);
          const width = Math.abs(currentPoints[1].x - currentPoints[0].x);
          const height = Math.abs(currentPoints[1].y - currentPoints[0].y);

          return (
            <rect
              x={x}
              y={y}
              width={width}
              height={height}
              fill="none"
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              strokeDasharray="5,5"
            />
          );
        }
        break;

      case 'circle':
        if (currentPoints.length >= 2) {
          const centerX = currentPoints[0].x;
          const centerY = currentPoints[0].y;
          const radius = Math.sqrt(
            Math.pow(currentPoints[1].x - centerX, 2) +
            Math.pow(currentPoints[1].y - centerY, 2)
          );

          return (
            <circle
              cx={centerX}
              cy={centerY}
              r={radius}
              fill="none"
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              strokeDasharray="5,5"
            />
          );
        }
        break;
    }
    return null;
  };

  return (
    <div style={{
      flex: 1,
      position: 'relative',
      overflow: 'auto',
      backgroundColor: '#f0f0f0',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <img
          src={image.url}
          alt={image.name}
          style={{
            maxWidth: '100%',
            maxHeight: '100%',
            display: 'block'
          }}
        />
        <svg
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            cursor: activeTool === 'select' ? 'default' : 'crosshair'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
        >
          {image.annotations.filter(ann => ann.visible).map(renderAnnotation)}
          {renderCurrentDrawing()}
        </svg>
      </div>
    </div>
  );
};

function App() {
  const [showTest, setShowTest] = useState(true);

  if (showTest) {
    return (
      <div>
        <TestComponent />
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <button
            onClick={() => setShowTest(false)}
            style={{
              padding: '10px 20px',
              backgroundColor: '#2196f3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            启动完整应用
          </button>
        </div>
      </div>
    );
  }

  return <MainApp />;
}

export default App;
