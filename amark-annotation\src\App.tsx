import React from 'react';

function App() {
  return (
    <div style={{
      padding: '20px',
      fontFamily: 'Arial, sans-serif',
      minHeight: '100vh',
      backgroundColor: '#f0f0f0'
    }}>
      <h1 style={{ color: '#2196f3', marginBottom: '20px' }}>
        AMARK 图像标注软件
      </h1>
      <p style={{ fontSize: '18px', marginBottom: '20px' }}>
        应用正在运行！
      </p>
      <div style={{
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h2>测试信息</h2>
        <ul>
          <li>React 组件正常渲染</li>
          <li>样式正常应用</li>
          <li>页面内容显示正常</li>
        </ul>
        <p style={{ color: '#666', marginTop: '20px' }}>
          如果您能看到这个页面，说明基础框架工作正常。
        </p>
      </div>
    </div>
  );
}

export default App;
