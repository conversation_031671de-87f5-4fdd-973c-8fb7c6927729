import React, { useState } from 'react';
import { Provider } from 'react-redux';
import { store } from './store';

// 简化的主应用组件
const MainApp: React.FC = () => {
  const [projectName, setProjectName] = useState('');
  const [currentProject, setCurrentProject] = useState<string | null>(null);
  const [images, setImages] = useState<Array<{id: string, name: string, url: string}>>([]);

  const handleCreateProject = () => {
    if (projectName.trim()) {
      setCurrentProject(projectName.trim());
      setProjectName('');
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = () => {
        const newImage = {
          id: Date.now().toString(),
          name: file.name,
          url: reader.result as string
        };
        setImages(prev => [...prev, newImage]);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        height: '60px',
        backgroundColor: '#2196f3',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px' }}>AMARK</h1>
        <span style={{ marginLeft: '20px', opacity: 0.9 }}>图像标注软件</span>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          backgroundColor: '#f5f5f5',
          borderRight: '1px solid #ddd',
          padding: '20px'
        }}>
          <h3 style={{ marginTop: 0 }}>项目管理</h3>

          {!currentProject ? (
            <div>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  项目名称:
                </label>
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="输入项目名称"
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    fontSize: '14px'
                  }}
                />
              </div>
              <button
                onClick={handleCreateProject}
                disabled={!projectName.trim()}
                style={{
                  width: '100%',
                  padding: '12px',
                  backgroundColor: projectName.trim() ? '#2196f3' : '#ccc',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                  fontSize: '16px',
                  fontWeight: 'bold'
                }}
              >
                创建项目
              </button>
            </div>
          ) : (
            <div>
              <div style={{
                padding: '15px',
                backgroundColor: '#e8f5e8',
                borderRadius: '6px',
                marginBottom: '20px',
                border: '1px solid #4caf50'
              }}>
                <p style={{ margin: '0 0 5px 0' }}>
                  <strong>当前项目:</strong> {currentProject}
                </p>
                <p style={{ margin: 0, color: '#666' }}>
                  <strong>图像数量:</strong> {images.length}
                </p>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                  上传图像:
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                />
              </div>

              {images.length > 0 && (
                <div>
                  <h4>图像列表:</h4>
                  {images.map((image) => (
                    <div key={image.id} style={{
                      padding: '10px',
                      backgroundColor: 'white',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}>
                      <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
                        {image.name}
                      </div>
                      <img
                        src={image.url}
                        alt={image.name}
                        style={{
                          width: '100%',
                          height: '60px',
                          objectFit: 'cover',
                          borderRadius: '4px'
                        }}
                      />
                    </div>
                  ))}
                </div>
              )}

              <button
                onClick={() => {
                  setCurrentProject(null);
                  setImages([]);
                }}
                style={{
                  width: '100%',
                  padding: '10px',
                  backgroundColor: '#ff5722',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginTop: '20px'
                }}
              >
                重置项目
              </button>
            </div>
          )}
        </div>

        {/* Main Area */}
        <div style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#fafafa',
          padding: '20px'
        }}>
          {currentProject && images.length > 0 ? (
            <div style={{ textAlign: 'center', maxWidth: '800px', width: '100%' }}>
              <h2>图像预览</h2>
              <div style={{
                border: '2px solid #ddd',
                borderRadius: '8px',
                overflow: 'hidden',
                backgroundColor: 'white',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}>
                <img
                  src={images[0].url}
                  alt={images[0].name}
                  style={{
                    width: '100%',
                    maxHeight: '500px',
                    objectFit: 'contain'
                  }}
                />
              </div>
              <p style={{ marginTop: '15px', color: '#666', fontSize: '16px' }}>
                {images[0].name}
              </p>
              <div style={{
                marginTop: '20px',
                padding: '15px',
                backgroundColor: '#e3f2fd',
                borderRadius: '6px',
                textAlign: 'left'
              }}>
                <h4 style={{ margin: '0 0 10px 0' }}>下一步功能:</h4>
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  <li>添加标注工具（点、线、矩形、圆形等）</li>
                  <li>样式定制（颜色、线宽、透明度）</li>
                  <li>图层管理和组织</li>
                  <li>数据导出（Excel、CSV、JSON）</li>
                </ul>
              </div>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666', maxWidth: '600px' }}>
              <h2>欢迎使用 AMARK 图像标注软件</h2>
              <p style={{ fontSize: '18px', marginBottom: '30px' }}>
                {!currentProject
                  ? '请先创建项目开始使用'
                  : '请上传图像开始标注'
                }
              </p>

              <div style={{
                padding: '30px',
                backgroundColor: 'white',
                borderRadius: '10px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}>
                <h3 style={{ color: '#2196f3', marginBottom: '20px' }}>🎯 功能特性</h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '20px',
                  textAlign: 'left'
                }}>
                  <div>
                    <h4 style={{ color: '#333', marginBottom: '10px' }}>标注工具</h4>
                    <ul style={{ lineHeight: '1.6', color: '#666' }}>
                      <li>点标记</li>
                      <li>线段</li>
                      <li>矩形</li>
                      <li>圆形</li>
                      <li>多边形</li>
                      <li>自由绘制</li>
                    </ul>
                  </div>
                  <div>
                    <h4 style={{ color: '#333', marginBottom: '10px' }}>数据管理</h4>
                    <ul style={{ lineHeight: '1.6', color: '#666' }}>
                      <li>项目管理</li>
                      <li>图层组织</li>
                      <li>Excel导出</li>
                      <li>CSV导出</li>
                      <li>JSON导出</li>
                      <li>样式定制</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

function App() {
  return (
    <Provider store={store}>
      <MainApp />
    </Provider>
  );
}

export default App;
