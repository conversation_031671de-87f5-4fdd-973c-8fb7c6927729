import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Stage, Layer, Image as KonvaImage, Line, Rect, Circle } from 'react-konva';
import { RootState } from '../../store';
import { addMarker, selectMarkers, updateViewport } from '../../store/slices/projectSlice';
import { Point, Marker } from '../../types';
import './ImageCanvas.css';

const ImageCanvas: React.FC = () => {
  const dispatch = useDispatch();
  const { 
    currentProject, 
    selectedImageId, 
    selectedMarkerIds, 
    activeToolType, 
    currentMarkerStyle,
    viewportState 
  } = useSelector((state: RootState) => state.project);

  const stageRef = useRef<any>(null);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentPoints, setCurrentPoints] = useState<Point[]>([]);
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 });

  const selectedImage = currentProject?.images.find(img => img.id === selectedImageId);

  // 加载图像
  useEffect(() => {
    if (selectedImage) {
      const img = new Image();
      img.onload = () => {
        setImage(img);
        // 调整舞台大小以适应图像
        const containerWidth = 800; // 这应该从容器获取
        const containerHeight = 600;
        const scale = Math.min(
          containerWidth / img.width,
          containerHeight / img.height
        );
        setStageSize({
          width: img.width * scale,
          height: img.height * scale
        });
      };
      img.src = selectedImage.url;
    }
  }, [selectedImage]);

  // 处理鼠标按下事件
  const handleMouseDown = (e: any) => {
    if (activeToolType === 'select') return;

    const pos = e.target.getStage().getPointerPosition();
    const point: Point = {
      x: pos.x / viewportState.zoom,
      y: pos.y / viewportState.zoom
    };

    setIsDrawing(true);
    setCurrentPoints([point]);
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: any) => {
    if (!isDrawing || activeToolType === 'select') return;

    const pos = e.target.getStage().getPointerPosition();
    const point: Point = {
      x: pos.x / viewportState.zoom,
      y: pos.y / viewportState.zoom
    };

    if (activeToolType === 'freehand') {
      setCurrentPoints(prev => [...prev, point]);
    } else if (activeToolType === 'line' || activeToolType === 'rectangle') {
      setCurrentPoints([currentPoints[0], point]);
    }
  };

  // 处理鼠标抬起事件
  const handleMouseUp = () => {
    if (!isDrawing || currentPoints.length === 0) return;

    const newMarker: Omit<Marker, 'id' | 'createdAt' | 'updatedAt'> = {
      type: activeToolType as Marker['type'],
      name: `${activeToolType} ${Date.now()}`,
      points: currentPoints,
      style: currentMarkerStyle,
      groupIds: [],
      tags: [],
      visible: true,
      locked: false,
      metadata: {}
    };

    dispatch(addMarker(newMarker));
    setIsDrawing(false);
    setCurrentPoints([]);
  };

  // 处理缩放
  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    
    const scaleBy = 1.1;
    const stage = e.target.getStage();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;
    
    // 限制缩放范围
    const clampedScale = Math.max(0.1, Math.min(4, newScale));
    
    dispatch(updateViewport({ zoom: clampedScale }));
    
    stage.scale({ x: clampedScale, y: clampedScale });
    
    // 调整位置以保持鼠标位置不变
    const newPos = {
      x: pointer.x - (pointer.x - stage.x()) * (clampedScale / oldScale),
      y: pointer.y - (pointer.y - stage.y()) * (clampedScale / oldScale),
    };
    
    stage.position(newPos);
    dispatch(updateViewport({ pan: newPos }));
  };

  // 渲染标注
  const renderMarker = (marker: Marker) => {
    const isSelected = selectedMarkerIds.includes(marker.id);
    const strokeColor = isSelected ? '#00ff00' : marker.style.color;
    const strokeWidth = marker.style.lineWidth * (isSelected ? 1.5 : 1);

    switch (marker.type) {
      case 'point':
        return (
          <Circle
            key={marker.id}
            x={marker.points[0]?.x || 0}
            y={marker.points[0]?.y || 0}
            radius={5}
            fill={strokeColor}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
            onClick={() => dispatch(selectMarkers([marker.id]))}
          />
        );

      case 'line':
        if (marker.points.length >= 2) {
          return (
            <Line
              key={marker.id}
              points={[
                marker.points[0].x, marker.points[0].y,
                marker.points[1].x, marker.points[1].y
              ]}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              onClick={() => dispatch(selectMarkers([marker.id]))}
            />
          );
        }
        break;

      case 'rectangle':
        if (marker.points.length >= 2) {
          const x = Math.min(marker.points[0].x, marker.points[1].x);
          const y = Math.min(marker.points[0].y, marker.points[1].y);
          const width = Math.abs(marker.points[1].x - marker.points[0].x);
          const height = Math.abs(marker.points[1].y - marker.points[0].y);
          
          return (
            <Rect
              key={marker.id}
              x={x}
              y={y}
              width={width}
              height={height}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              fill={marker.style.fillColor}
              onClick={() => dispatch(selectMarkers([marker.id]))}
            />
          );
        }
        break;

      case 'freehand':
        if (marker.points.length > 1) {
          const points = marker.points.flatMap(p => [p.x, p.y]);
          return (
            <Line
              key={marker.id}
              points={points}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              tension={0.5}
              lineCap="round"
              lineJoin="round"
              onClick={() => dispatch(selectMarkers([marker.id]))}
            />
          );
        }
        break;

      default:
        return null;
    }
    return null;
  };

  // 渲染当前绘制的标注
  const renderCurrentDrawing = () => {
    if (!isDrawing || currentPoints.length === 0) return null;

    const strokeColor = currentMarkerStyle.color;
    const strokeWidth = currentMarkerStyle.lineWidth;

    switch (activeToolType) {
      case 'line':
        if (currentPoints.length >= 2) {
          return (
            <Line
              points={[
                currentPoints[0].x, currentPoints[0].y,
                currentPoints[1].x, currentPoints[1].y
              ]}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              dash={[5, 5]}
            />
          );
        }
        break;

      case 'rectangle':
        if (currentPoints.length >= 2) {
          const x = Math.min(currentPoints[0].x, currentPoints[1].x);
          const y = Math.min(currentPoints[0].y, currentPoints[1].y);
          const width = Math.abs(currentPoints[1].x - currentPoints[0].x);
          const height = Math.abs(currentPoints[1].y - currentPoints[0].y);
          
          return (
            <Rect
              x={x}
              y={y}
              width={width}
              height={height}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              fill={currentMarkerStyle.fillColor}
              dash={[5, 5]}
            />
          );
        }
        break;

      case 'freehand':
        if (currentPoints.length > 1) {
          const points = currentPoints.flatMap(p => [p.x, p.y]);
          return (
            <Line
              points={points}
              stroke={strokeColor}
              strokeWidth={strokeWidth}
              tension={0.5}
              lineCap="round"
              lineJoin="round"
            />
          );
        }
        break;

      default:
        return null;
    }
    return null;
  };

  if (!selectedImage || !image) {
    return (
      <div className="canvas-container">
        <div className="canvas-placeholder">
          <p>请选择一个图像开始标注</p>
        </div>
      </div>
    );
  }

  return (
    <div className="canvas-container">
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        scaleX={viewportState.zoom}
        scaleY={viewportState.zoom}
        x={viewportState.pan.x}
        y={viewportState.pan.y}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onWheel={handleWheel}
        draggable={activeToolType === 'select'}
      >
        <Layer>
          {/* 背景图像 */}
          <KonvaImage
            image={image}
            width={image.width}
            height={image.height}
          />
          
          {/* 现有标注 */}
          {selectedImage.markers
            .filter(marker => marker.visible)
            .map(renderMarker)}
          
          {/* 当前绘制的标注 */}
          {renderCurrentDrawing()}
        </Layer>
      </Stage>
    </div>
  );
};

export default ImageCanvas;
