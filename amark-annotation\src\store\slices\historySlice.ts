import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface HistoryEntry {
  id: string;
  action: string;
  timestamp: Date;
  data: any;
  description: string;
}

interface HistoryState {
  past: HistoryEntry[];
  present?: HistoryEntry;
  future: HistoryEntry[];
  maxSteps: number;
}

const initialState: HistoryState = {
  past: [],
  future: [],
  maxSteps: 50,
};

const historySlice = createSlice({
  name: 'history',
  initialState,
  reducers: {
    addHistoryEntry: (state, action: PayloadAction<{
      action: string;
      data: any;
      description: string;
    }>) => {
      const entry: HistoryEntry = {
        id: crypto.randomUUID(),
        timestamp: new Date(),
        ...action.payload,
      };

      // Move current present to past
      if (state.present) {
        state.past.push(state.present);
      }

      // Set new present
      state.present = entry;

      // Clear future (new action invalidates redo history)
      state.future = [];

      // Limit past entries to maxSteps
      if (state.past.length > state.maxSteps) {
        state.past = state.past.slice(-state.maxSteps);
      }
    },

    undo: (state) => {
      if (state.past.length === 0) return;

      const previous = state.past.pop()!;
      
      if (state.present) {
        state.future.unshift(state.present);
      }
      
      state.present = previous;
    },

    redo: (state) => {
      if (state.future.length === 0) return;

      const next = state.future.shift()!;
      
      if (state.present) {
        state.past.push(state.present);
      }
      
      state.present = next;
    },

    clearHistory: (state) => {
      state.past = [];
      state.present = undefined;
      state.future = [];
    },

    setMaxSteps: (state, action: PayloadAction<number>) => {
      state.maxSteps = action.payload;
      
      // Trim past entries if necessary
      if (state.past.length > state.maxSteps) {
        state.past = state.past.slice(-state.maxSteps);
      }
    },
  },
});

export const {
  addHistoryEntry,
  undo,
  redo,
  clearHistory,
  setMaxSteps,
} = historySlice.actions;

export default historySlice.reducer;
