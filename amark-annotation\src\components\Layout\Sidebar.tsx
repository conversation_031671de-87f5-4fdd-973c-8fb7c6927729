import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useDropzone } from 'react-dropzone';
import { RootState } from '../../store';
import { addImage, selectImage } from '../../store/slices/projectSlice';
import { addNotification } from '../../store/slices/uiSlice';
import { Upload, Image, Folder } from 'lucide-react';
import './Sidebar.css';

const Sidebar: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject, selectedImageId } = useSelector((state: RootState) => state.project);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = () => {
          const img = new Image();
          img.onload = () => {
            dispatch(addImage({
              file,
              url: reader.result as string,
              size: { width: img.width, height: img.height }
            }));
            dispatch(addNotification({
              type: 'success',
              message: `图像 "${file.name}" 已成功添加`
            }));
          };
          img.src = reader.result as string;
        };
        reader.readAsDataURL(file);
      } else {
        dispatch(addNotification({
          type: 'error',
          message: `文件 "${file.name}" 不是有效的图像格式`
        }));
      }
    });
  }, [dispatch]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.bmp', '.tiff', '.webp']
    },
    maxSize: 100 * 1024 * 1024, // 100MB
  });

  if (!currentProject) {
    return (
      <div className="sidebar">
        <div className="sidebar-section">
          <h3>项目</h3>
          <p className="empty-state">请先创建或打开一个项目</p>
        </div>
      </div>
    );
  }

  return (
    <div className="sidebar">
      <div className="sidebar-section">
        <h3>
          <Folder size={16} />
          项目图像
        </h3>
        
        <div 
          {...getRootProps()} 
          className={`dropzone ${isDragActive ? 'active' : ''}`}
        >
          <input {...getInputProps()} />
          <Upload size={24} />
          <p>
            {isDragActive 
              ? '拖放图像到这里...' 
              : '拖放图像或点击选择文件'
            }
          </p>
          <small>支持 JPEG, PNG, BMP, TIFF 格式，最大 100MB</small>
        </div>
        
        <div className="image-list">
          {currentProject.images.length === 0 ? (
            <p className="empty-state">暂无图像</p>
          ) : (
            currentProject.images.map((image) => (
              <div 
                key={image.id}
                className={`image-item ${selectedImageId === image.id ? 'selected' : ''}`}
                onClick={() => dispatch(selectImage(image.id))}
              >
                <div className="image-thumbnail">
                  <img src={image.url} alt={image.name} />
                </div>
                <div className="image-info">
                  <div className="image-name" title={image.name}>
                    {image.name}
                  </div>
                  <div className="image-details">
                    {image.size.width} × {image.size.height}
                  </div>
                  <div className="marker-count">
                    {image.markers.length} 个标注
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      
      <div className="sidebar-section">
        <h3>
          <Image size={16} />
          图像信息
        </h3>
        
        {selectedImageId ? (
          (() => {
            const selectedImage = currentProject.images.find(img => img.id === selectedImageId);
            return selectedImage ? (
              <div className="image-details-panel">
                <div className="detail-row">
                  <label>文件名:</label>
                  <span>{selectedImage.name}</span>
                </div>
                <div className="detail-row">
                  <label>尺寸:</label>
                  <span>{selectedImage.size.width} × {selectedImage.size.height}</span>
                </div>
                <div className="detail-row">
                  <label>缩放:</label>
                  <span>{(selectedImage.scale * 100).toFixed(1)}%</span>
                </div>
                <div className="detail-row">
                  <label>旋转:</label>
                  <span>{selectedImage.rotation}°</span>
                </div>
                <div className="detail-row">
                  <label>标注数量:</label>
                  <span>{selectedImage.markers.length}</span>
                </div>
              </div>
            ) : null;
          })()
        ) : (
          <p className="empty-state">请选择一个图像</p>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
