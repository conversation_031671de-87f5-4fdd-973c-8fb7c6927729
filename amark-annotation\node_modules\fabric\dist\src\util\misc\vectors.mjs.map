{"version": 3, "file": "vectors.mjs", "sources": ["../../../../src/util/misc/vectors.ts"], "sourcesContent": ["import type { XY } from '../../Point';\nimport { Point } from '../../Point';\nimport type { TRadian } from '../../typedefs';\n\nconst unitVectorX = new Point(1, 0);\nconst zero = new Point();\n\n/**\n * Rotates `vector` with `radians`\n * @param {Point} vector The vector to rotate (x and y)\n * @param {Number} radians The radians of the angle for the rotation\n * @return {Point} The new rotated point\n */\nexport const rotateVector = (vector: Point, radians: TRadian) =>\n  vector.rotate(radians);\n\n/**\n * Creates a vector from points represented as a point\n *\n * @param {Point} from\n * @param {Point} to\n * @returns {Point} vector\n */\nexport const createVector = (from: XY, to: XY): Point =>\n  new Point(to).subtract(from);\n\n/**\n * return the magnitude of a vector\n * @return {number}\n */\nexport const magnitude = (point: Point) => point.distanceFrom(zero);\n\n/**\n * Calculates the angle between 2 vectors\n * @param {Point} a\n * @param {Point} b\n * @returns the angle in radians from `a` to `b`\n */\nexport const calcAngleBetweenVectors = (a: Point, b: Point): TRadian =>\n  Math.atan2(crossProduct(a, b), dotProduct(a, b)) as TRadian;\n\n/**\n * Calculates the angle between the x axis and the vector\n * @param {Point} v\n * @returns the angle in radians of `v`\n */\nexport const calcVectorRotation = (v: Point) =>\n  calcAngleBetweenVectors(unitVectorX, v);\n\n/**\n * @param {Point} v\n * @returns {Point} vector representing the unit vector pointing to the direction of `v`\n */\nexport const getUnitVector = (v: Point): Point =>\n  v.eq(zero) ? v : v.scalarDivide(magnitude(v));\n\n/**\n * @param {Point} v\n * @param {Boolean} [counterClockwise] the direction of the orthogonal vector, defaults to `true`\n * @returns {Point} the unit orthogonal vector\n */\nexport const getOrthonormalVector = (\n  v: Point,\n  counterClockwise = true,\n): Point =>\n  getUnitVector(new Point(-v.y, v.x).scalarMultiply(counterClockwise ? 1 : -1));\n\n/**\n * Cross product of two vectors in 2D\n * @param {Point} a\n * @param {Point} b\n * @returns {number} the magnitude of Z vector\n */\nexport const crossProduct = (a: Point, b: Point): number =>\n  a.x * b.y - a.y * b.x;\n\n/**\n * Dot product of two vectors in 2D\n * @param {Point} a\n * @param {Point} b\n * @returns {number}\n */\nexport const dotProduct = (a: Point, b: Point): number => a.x * b.x + a.y * b.y;\n\n/**\n * Checks if the vector is between two others. It is considered\n * to be inside when the vector to be tested is between the\n * initial vector and the final vector (included) in a counterclockwise direction.\n * @param {Point} t vector to be tested\n * @param {Point} a initial vector\n * @param {Point} b final vector\n * @returns {boolean} true if the vector is among the others\n */\nexport const isBetweenVectors = (t: Point, a: Point, b: Point): boolean => {\n  if (t.eq(a) || t.eq(b)) return true;\n  const AxB = crossProduct(a, b),\n    AxT = crossProduct(a, t),\n    BxT = crossProduct(b, t);\n  return AxB >= 0 ? AxT >= 0 && BxT <= 0 : !(AxT <= 0 && BxT >= 0);\n};\n"], "names": ["unitVectorX", "Point", "zero", "rotateVector", "vector", "radians", "rotate", "createVector", "from", "to", "subtract", "magnitude", "point", "distanceFrom", "calcAngleBetweenVectors", "a", "b", "Math", "atan2", "crossProduct", "dotProduct", "calcVectorRotation", "v", "getUnitVector", "eq", "scalarDivide", "getOrthonormalVector", "counterClockwise", "arguments", "length", "undefined", "y", "x", "scalar<PERSON>ultiply", "isBetweenVectors", "t", "AxB", "AxT", "BxT"], "mappings": ";;AAIA,MAAMA,WAAW,GAAG,IAAIC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACnC,MAAMC,IAAI,GAAG,IAAID,KAAK,EAAE,CAAA;;AAExB;AACA;AACA;AACA;AACA;AACA;AACaE,MAAAA,YAAY,GAAGA,CAACC,MAAa,EAAEC,OAAgB,KAC1DD,MAAM,CAACE,MAAM,CAACD,OAAO,EAAC;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;MACaE,YAAY,GAAGA,CAACC,IAAQ,EAAEC,EAAM,KAC3C,IAAIR,KAAK,CAACQ,EAAE,CAAC,CAACC,QAAQ,CAACF,IAAI,EAAC;;AAE9B;AACA;AACA;AACA;AACO,MAAMG,SAAS,GAAIC,KAAY,IAAKA,KAAK,CAACC,YAAY,CAACX,IAAI,EAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACO,MAAMY,uBAAuB,GAAGA,CAACC,CAAQ,EAAEC,CAAQ,KACxDC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACJ,CAAC,EAAEC,CAAC,CAAC,EAAEI,UAAU,CAACL,CAAC,EAAEC,CAAC,CAAC,EAAY;;AAE7D;AACA;AACA;AACA;AACA;AACO,MAAMK,kBAAkB,GAAIC,CAAQ,IACzCR,uBAAuB,CAACd,WAAW,EAAEsB,CAAC,EAAC;;AAEzC;AACA;AACA;AACA;AACO,MAAMC,aAAa,GAAID,CAAQ,IACpCA,CAAC,CAACE,EAAE,CAACtB,IAAI,CAAC,GAAGoB,CAAC,GAAGA,CAAC,CAACG,YAAY,CAACd,SAAS,CAACW,CAAC,CAAC,EAAC;;AAE/C;AACA;AACA;AACA;AACA;AACaI,MAAAA,oBAAoB,GAAG,UAClCJ,CAAQ,EAAA;AAAA,EAAA,IACRK,gBAAgB,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;EAAA,OAEvBL,aAAa,CAAC,IAAItB,KAAK,CAAC,CAACqB,CAAC,CAACS,CAAC,EAAET,CAAC,CAACU,CAAC,CAAC,CAACC,cAAc,CAACN,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;AAAA,EAAA;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACO,MAAMR,YAAY,GAAGA,CAACJ,CAAQ,EAAEC,CAAQ,KAC7CD,CAAC,CAACiB,CAAC,GAAGhB,CAAC,CAACe,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAGf,CAAC,CAACgB,EAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACO,MAAMZ,UAAU,GAAGA,CAACL,CAAQ,EAAEC,CAAQ,KAAaD,CAAC,CAACiB,CAAC,GAAGhB,CAAC,CAACgB,CAAC,GAAGjB,CAAC,CAACgB,CAAC,GAAGf,CAAC,CAACe,EAAC;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,gBAAgB,GAAGA,CAACC,CAAQ,EAAEpB,CAAQ,EAAEC,CAAQ,KAAc;AACzE,EAAA,IAAImB,CAAC,CAACX,EAAE,CAACT,CAAC,CAAC,IAAIoB,CAAC,CAACX,EAAE,CAACR,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;AACnC,EAAA,MAAMoB,GAAG,GAAGjB,YAAY,CAACJ,CAAC,EAAEC,CAAC,CAAC;AAC5BqB,IAAAA,GAAG,GAAGlB,YAAY,CAACJ,CAAC,EAAEoB,CAAC,CAAC;AACxBG,IAAAA,GAAG,GAAGnB,YAAY,CAACH,CAAC,EAAEmB,CAAC,CAAC,CAAA;EAC1B,OAAOC,GAAG,IAAI,CAAC,GAAGC,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,GAAG,EAAED,GAAG,IAAI,CAAC,IAAIC,GAAG,IAAI,CAAC,CAAC,CAAA;AAClE;;;;"}