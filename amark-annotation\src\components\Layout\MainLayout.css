.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

.main-layout.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #e0e0e0;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #d0d0d0;
  --accent-color: #007acc;
  --hover-color: #f0f0f0;
}

.main-layout.dark {
  --bg-primary: #1e1e1e;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3e3e3e;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --border-color: #555555;
  --accent-color: #0078d4;
  --hover-color: #404040;
}

.layout-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar-container {
  width: 250px;
  min-width: 200px;
  max-width: 400px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  resize: horizontal;
  overflow: auto;
}

.main-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.toolbar-container {
  height: 50px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.canvas-container {
  flex: 1;
  background-color: var(--bg-primary);
  position: relative;
  overflow: hidden;
}

.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  text-align: center;
}

.welcome-screen h2 {
  margin-bottom: 10px;
  color: var(--text-primary);
}

.right-panels {
  display: flex;
  flex-direction: column;
  width: 300px;
  min-width: 250px;
  max-width: 500px;
  background-color: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  resize: horizontal;
}

.layers-panel-container {
  flex: 1;
  min-height: 200px;
  border-bottom: 1px solid var(--border-color);
}

.properties-panel-container {
  flex: 1;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar-container {
    width: 200px;
  }
  
  .right-panels {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .layout-body {
    flex-direction: column;
  }
  
  .sidebar-container,
  .right-panels {
    width: 100%;
    height: 200px;
    resize: vertical;
  }
  
  .main-content {
    flex: 1;
  }
}
