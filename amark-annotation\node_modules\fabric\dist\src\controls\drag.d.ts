import type { TransformActionHandler } from '../EventTypeDefs';
/**
 * Action handler
 * @private
 * @param {Event} eventData javascript event that is doing the transform
 * @param {Object} transform javascript object containing a series of information around the current transform
 * @param {number} x current mouse x position, canvas normalized
 * @param {number} y current mouse y position, canvas normalized
 * @return {Boolean} true if the translation occurred
 */
export declare const dragHandler: TransformActionHandler;
//# sourceMappingURL=drag.d.ts.map