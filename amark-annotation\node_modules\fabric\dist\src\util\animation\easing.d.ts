/**
 * Easing functions
 * @see {@link http://gizma.com/easing/ Easing Equations by <PERSON>}
 */
import type { TEasingFunction } from './types';
/**
 * Default sinusoidal easing
 */
export declare const defaultEasing: TEasingFunction;
/**
 * Cubic easing in
 */
export declare const easeInCubic: TEasingFunction;
/**
 * Cubic easing out
 */
export declare const easeOutCubic: TEasingFunction;
/**
 * Cubic easing in and out
 */
export declare const easeInOutCubic: TEasingFunction;
/**
 * Quartic easing in
 */
export declare const easeInQuart: TEasingFunction;
/**
 * Quartic easing out
 */
export declare const easeOutQuart: TEasingFunction;
/**
 * Quartic easing in and out
 */
export declare const easeInOutQuart: TEasingFunction;
/**
 * Quintic easing in
 */
export declare const easeInQuint: TEasingFunction;
/**
 * Quintic easing out
 */
export declare const easeOutQuint: TEasingFunction;
/**
 * Quintic easing in and out
 */
export declare const easeInOutQuint: TEasingFunction;
/**
 * Sinusoidal easing in
 */
export declare const easeInSine: TEasingFunction;
/**
 * Sinusoidal easing out
 */
export declare const easeOutSine: TEasingFunction;
/**
 * Sinusoidal easing in and out
 */
export declare const easeInOutSine: TEasingFunction;
/**
 * Exponential easing in
 */
export declare const easeInExpo: TEasingFunction;
/**
 * Exponential easing out
 */
export declare const easeOutExpo: TEasingFunction;
/**
 * Exponential easing in and out
 */
export declare const easeInOutExpo: TEasingFunction;
/**
 * Circular easing in
 */
export declare const easeInCirc: TEasingFunction;
/**
 * Circular easing out
 */
export declare const easeOutCirc: TEasingFunction;
/**
 * Circular easing in and out
 */
export declare const easeInOutCirc: TEasingFunction;
/**
 * Elastic easing in
 */
export declare const easeInElastic: TEasingFunction;
/**
 * Elastic easing out
 */
export declare const easeOutElastic: TEasingFunction;
/**
 * Elastic easing in and out
 */
export declare const easeInOutElastic: TEasingFunction;
/**
 * Backwards easing in
 */
export declare const easeInBack: TEasingFunction;
/**
 * Backwards easing out
 */
export declare const easeOutBack: TEasingFunction;
/**
 * Backwards easing in and out
 */
export declare const easeInOutBack: TEasingFunction;
/**
 * Bouncing easing out
 */
export declare const easeOutBounce: TEasingFunction;
/**
 * Bouncing easing in
 */
export declare const easeInBounce: TEasingFunction;
/**
 * Bouncing easing in and out
 */
export declare const easeInOutBounce: TEasingFunction;
/**
 * Quadratic easing in
 */
export declare const easeInQuad: TEasingFunction;
/**
 * Quadratic easing out
 */
export declare const easeOutQuad: TEasingFunction;
/**
 * Quadratic easing in and out
 */
export declare const easeInOutQuad: TEasingFunction;
//# sourceMappingURL=easing.d.ts.map