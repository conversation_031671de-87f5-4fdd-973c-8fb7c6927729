.export-dialog {
  background-color: var(--bg-primary);
  border-radius: 12px;
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.dialog-header h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: 24px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: var(--hover-color);
  color: var(--text-primary);
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.export-options {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-group h3 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.format-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.format-option {
  display: block;
  cursor: pointer;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s;
  background-color: var(--bg-secondary);
}

.format-option:hover {
  border-color: var(--accent-color);
  background-color: var(--hover-color);
}

.format-option.selected {
  border-color: var(--accent-color);
  background-color: rgba(0, 120, 204, 0.1);
}

.format-option input[type="radio"] {
  display: none;
}

.format-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.format-content svg {
  color: var(--accent-color);
  flex-shrink: 0;
}

.format-content span {
  font-weight: 600;
  color: var(--text-primary);
}

.format-content small {
  color: var(--text-secondary);
  font-size: 12px;
  margin-left: auto;
}

.checkbox-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 0;
}

.checkbox-option input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--accent-color);
}

.checkbox-option span {
  color: var(--text-primary);
  font-size: 14px;
}

.export-preview {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
}

.export-preview h3 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
}

.preview-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.stat-label {
  color: var(--text-secondary);
  font-size: 13px;
}

.stat-value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 13px;
}

.marker-types h4 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 600;
}

.type-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.type-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--bg-primary);
  border-radius: 4px;
}

.type-name {
  color: var(--text-primary);
  font-size: 12px;
  text-transform: capitalize;
}

.type-count {
  color: var(--accent-color);
  font-weight: 600;
  font-size: 12px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.button.primary {
  background-color: var(--accent-color);
  color: white;
}

.button.primary:hover {
  opacity: 0.9;
}

.button.secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.button.secondary:hover {
  background-color: var(--hover-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .export-dialog {
    width: 95vw;
    max-height: 95vh;
  }
  
  .dialog-content {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }
  
  .format-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .format-content small {
    margin-left: 0;
  }
}

/* 滚动条样式 */
.dialog-content::-webkit-scrollbar {
  width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
