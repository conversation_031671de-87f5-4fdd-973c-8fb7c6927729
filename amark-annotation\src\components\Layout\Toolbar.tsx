import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { setActiveToolType, setCurrentMarkerStyle } from '../../store/slices/projectSlice';
import { undo, redo } from '../../store/slices/historySlice';
import { 
  MousePointer, 
  Circle, 
  Square, 
  Minus, 
  MapPin, 
  Polygon, 
  Pen,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Move
} from 'lucide-react';
import './Toolbar.css';

const Toolbar: React.FC = () => {
  const dispatch = useDispatch();
  const { activeToolType, currentMarkerStyle } = useSelector((state: RootState) => state.project);
  const { past, future } = useSelector((state: RootState) => state.history);

  const tools = [
    { type: 'select' as const, icon: MousePointer, label: '选择' },
    { type: 'point' as const, icon: MapPin, label: '点标记' },
    { type: 'line' as const, icon: Minus, label: '线段' },
    { type: 'rectangle' as const, icon: Square, label: '矩形' },
    { type: 'circle' as const, icon: Circle, label: '圆形' },
    { type: 'polygon' as const, icon: Polygon, label: '多边形' },
    { type: 'freehand' as const, icon: Pen, label: '自由绘制' },
  ];

  const handleToolSelect = (toolType: typeof activeToolType) => {
    dispatch(setActiveToolType(toolType));
  };

  const handleColorChange = (color: string) => {
    dispatch(setCurrentMarkerStyle({
      ...currentMarkerStyle,
      color
    }));
  };

  const handleLineWidthChange = (lineWidth: number) => {
    dispatch(setCurrentMarkerStyle({
      ...currentMarkerStyle,
      lineWidth
    }));
  };

  return (
    <div className="toolbar">
      <div className="toolbar-section">
        <div className="tool-group">
          {tools.map((tool) => {
            const Icon = tool.icon;
            return (
              <button
                key={tool.type}
                className={`tool-button ${activeToolType === tool.type ? 'active' : ''}`}
                onClick={() => handleToolSelect(tool.type)}
                title={tool.label}
              >
                <Icon size={18} />
              </button>
            );
          })}
        </div>
      </div>

      <div className="toolbar-section">
        <div className="tool-group">
          <button
            className="tool-button"
            onClick={() => dispatch(undo())}
            disabled={past.length === 0}
            title="撤销"
          >
            <Undo size={18} />
          </button>
          <button
            className="tool-button"
            onClick={() => dispatch(redo())}
            disabled={future.length === 0}
            title="重做"
          >
            <Redo size={18} />
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <div className="tool-group">
          <button className="tool-button" title="放大">
            <ZoomIn size={18} />
          </button>
          <button className="tool-button" title="缩小">
            <ZoomOut size={18} />
          </button>
          <button className="tool-button" title="平移">
            <Move size={18} />
          </button>
          <button className="tool-button" title="重置旋转">
            <RotateCcw size={18} />
          </button>
        </div>
      </div>

      <div className="toolbar-section">
        <div className="style-controls">
          <div className="control-group">
            <label>颜色:</label>
            <input
              type="color"
              value={currentMarkerStyle.color}
              onChange={(e) => handleColorChange(e.target.value)}
              className="color-picker"
            />
          </div>
          
          <div className="control-group">
            <label>线宽:</label>
            <input
              type="range"
              min="1"
              max="10"
              step="0.5"
              value={currentMarkerStyle.lineWidth}
              onChange={(e) => handleLineWidthChange(parseFloat(e.target.value))}
              className="line-width-slider"
            />
            <span className="value-display">{currentMarkerStyle.lineWidth}px</span>
          </div>
          
          <div className="control-group">
            <label>样式:</label>
            <select
              value={currentMarkerStyle.lineStyle}
              onChange={(e) => dispatch(setCurrentMarkerStyle({
                ...currentMarkerStyle,
                lineStyle: e.target.value as 'solid' | 'dashed' | 'dotted'
              }))}
              className="line-style-select"
            >
              <option value="solid">实线</option>
              <option value="dashed">虚线</option>
              <option value="dotted">点线</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;
