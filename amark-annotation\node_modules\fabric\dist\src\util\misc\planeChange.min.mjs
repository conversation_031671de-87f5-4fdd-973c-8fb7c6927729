import{iMatrix as t}from"../../constants.min.mjs";import{multiplyTransformMatrices as n,invertTransform as o}from"./matrix.min.mjs";import{applyTransformToObject as r}from"./objectTransforms.min.mjs";const i=function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:t;return n(o(arguments.length>1&&void 0!==arguments[1]?arguments[1]:t),r)},m=function(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;return n.transform(i(o,r))},e=function(n){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;return n.transform(i(o,r),!0)},s=(t,o,m)=>{const e=i(o,m);return r(t,n(e,t.calcOwnMatrix())),e};export{i as calcPlaneChangeMatrix,s as sendObjectToPlane,m as sendPointToPlane,e as sendVectorToPlane};
//# sourceMappingURL=planeChange.min.mjs.map
