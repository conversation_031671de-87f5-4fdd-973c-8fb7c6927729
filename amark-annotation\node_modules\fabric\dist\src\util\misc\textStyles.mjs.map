{"version": 3, "file": "textStyles.mjs", "sources": ["../../../../src/util/misc/textStyles.ts"], "sourcesContent": ["import { reNewline } from '../../constants';\nimport type {\n  TextStyle,\n  TextStyleDeclaration,\n} from '../../shapes/Text/StyledText';\nimport { cloneStyles } from '../internals/cloneStyles';\nimport { graphemeSplit } from '../lang_string';\n\nexport type TextStyleArray = {\n  start: number;\n  end: number;\n  style: TextStyleDeclaration;\n}[];\n\n/**\n * @param {Object} prevStyle first style to compare\n * @param {Object} thisStyle second style to compare\n * @param {boolean} forTextSpans whether to check overline, underline, and line-through properties\n * @return {boolean} true if the style changed\n */\nexport const hasStyleChanged = (\n  prevStyle: TextStyleDeclaration,\n  thisStyle: TextStyleDeclaration,\n  forTextSpans = false,\n) =>\n  prevStyle.fill !== thisStyle.fill ||\n  prevStyle.stroke !== thisStyle.stroke ||\n  prevStyle.strokeWidth !== thisStyle.strokeWidth ||\n  prevStyle.fontSize !== thisStyle.fontSize ||\n  prevStyle.fontFamily !== thisStyle.fontFamily ||\n  prevStyle.fontWeight !== thisStyle.fontWeight ||\n  prevStyle.fontStyle !== thisStyle.fontStyle ||\n  prevStyle.textBackgroundColor !== thisStyle.textBackgroundColor ||\n  prevStyle.deltaY !== thisStyle.deltaY ||\n  (forTextSpans &&\n    (prevStyle.overline !== thisStyle.overline ||\n      prevStyle.underline !== thisStyle.underline ||\n      prevStyle.linethrough !== thisStyle.linethrough));\n\n/**\n * Returns the array form of a text object's inline styles property with styles grouped in ranges\n * rather than per character. This format is less verbose, and is better suited for storage\n * so it is used in serialization (not during runtime).\n * @param {object} styles per character styles for a text object\n * @param {String} text the text string that the styles are applied to\n * @return {{start: number, end: number, style: object}[]}\n */\nexport const stylesToArray = (\n  styles: TextStyle,\n  text: string,\n): TextStyleArray => {\n  const textLines = text.split('\\n'),\n    stylesArray = [];\n  let charIndex = -1,\n    prevStyle = {};\n  // clone style structure to prevent mutation\n  styles = cloneStyles(styles);\n\n  //loop through each textLine\n  for (let i = 0; i < textLines.length; i++) {\n    const chars = graphemeSplit(textLines[i]);\n    if (!styles[i]) {\n      //no styles exist for this line, so add the line's length to the charIndex total and reset prevStyle\n      charIndex += chars.length;\n      prevStyle = {};\n      continue;\n    }\n    //loop through each character of the current line\n    for (let c = 0; c < chars.length; c++) {\n      charIndex++;\n      const thisStyle = styles[i][c];\n      //check if style exists for this character\n      if (thisStyle && Object.keys(thisStyle).length > 0) {\n        if (hasStyleChanged(prevStyle, thisStyle, true)) {\n          stylesArray.push({\n            start: charIndex,\n            end: charIndex + 1,\n            style: thisStyle,\n          });\n        } else {\n          //if style is the same as previous character, increase end index\n          stylesArray[stylesArray.length - 1].end++;\n        }\n      }\n      prevStyle = thisStyle || {};\n    }\n  }\n  return stylesArray;\n};\n\n/**\n * Returns the object form of the styles property with styles that are assigned per\n * character rather than grouped by range. This format is more verbose, and is\n * only used during runtime (not for serialization/storage)\n * @param {Array} styles the serialized form of a text object's styles\n * @param {String} text the text string that the styles are applied to\n * @return {Object}\n */\nexport const stylesFromArray = (\n  styles: TextStyleArray | TextStyle,\n  text: string,\n): TextStyle => {\n  if (!Array.isArray(styles)) {\n    // clone to prevent mutation\n    return cloneStyles(styles);\n  }\n  const textLines = text.split(reNewline),\n    stylesObject: TextStyle = {};\n  let charIndex = -1,\n    styleIndex = 0;\n  //loop through each textLine\n  for (let i = 0; i < textLines.length; i++) {\n    const chars = graphemeSplit(textLines[i]);\n\n    //loop through each character of the current line\n    for (let c = 0; c < chars.length; c++) {\n      charIndex++;\n      //check if there's a style collection that includes the current character\n      if (\n        styles[styleIndex] &&\n        styles[styleIndex].start <= charIndex &&\n        charIndex < styles[styleIndex].end\n      ) {\n        //create object for line index if it doesn't exist\n        stylesObject[i] = stylesObject[i] || {};\n        //assign a style at this character's index\n        stylesObject[i][c] = { ...styles[styleIndex].style };\n        //if character is at the end of the current style collection, move to the next\n        if (charIndex === styles[styleIndex].end - 1) {\n          styleIndex++;\n        }\n      }\n    }\n  }\n  return stylesObject;\n};\n"], "names": ["hasStyleChanged", "prevStyle", "thisStyle", "forTextSpans", "arguments", "length", "undefined", "fill", "stroke", "strokeWidth", "fontSize", "fontFamily", "fontWeight", "fontStyle", "textBackgroundColor", "deltaY", "overline", "underline", "linethrough", "stylesToArray", "styles", "text", "textLines", "split", "stylesArray", "charIndex", "cloneStyles", "i", "chars", "graphemeSplit", "c", "Object", "keys", "push", "start", "end", "style", "stylesFromArray", "Array", "isArray", "reNewline", "stylesObject", "styleIndex", "_objectSpread"], "mappings": ";;;;;AAcA;AACA;AACA;AACA;AACA;AACA;MACaA,eAAe,GAAG,UAC7BC,SAA+B,EAC/BC,SAA+B,EAAA;AAAA,EAAA,IAC/BC,YAAY,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,KAAK,CAAA;EAAA,OAEpBH,SAAS,CAACM,IAAI,KAAKL,SAAS,CAACK,IAAI,IACjCN,SAAS,CAACO,MAAM,KAAKN,SAAS,CAACM,MAAM,IACrCP,SAAS,CAACQ,WAAW,KAAKP,SAAS,CAACO,WAAW,IAC/CR,SAAS,CAACS,QAAQ,KAAKR,SAAS,CAACQ,QAAQ,IACzCT,SAAS,CAACU,UAAU,KAAKT,SAAS,CAACS,UAAU,IAC7CV,SAAS,CAACW,UAAU,KAAKV,SAAS,CAACU,UAAU,IAC7CX,SAAS,CAACY,SAAS,KAAKX,SAAS,CAACW,SAAS,IAC3CZ,SAAS,CAACa,mBAAmB,KAAKZ,SAAS,CAACY,mBAAmB,IAC/Db,SAAS,CAACc,MAAM,KAAKb,SAAS,CAACa,MAAM,IACpCZ,YAAY,KACVF,SAAS,CAACe,QAAQ,KAAKd,SAAS,CAACc,QAAQ,IACxCf,SAAS,CAACgB,SAAS,KAAKf,SAAS,CAACe,SAAS,IAC3ChB,SAAS,CAACiB,WAAW,KAAKhB,SAAS,CAACgB,WAAW,CAAE,CAAA;AAAA,EAAA;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACaC,aAAa,GAAGA,CAC3BC,MAAiB,EACjBC,IAAY,KACO;AACnB,EAAA,MAAMC,SAAS,GAAGD,IAAI,CAACE,KAAK,CAAC,IAAI,CAAC;AAChCC,IAAAA,WAAW,GAAG,EAAE,CAAA;EAClB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAChBxB,SAAS,GAAG,EAAE,CAAA;AAChB;AACAmB,EAAAA,MAAM,GAAGM,WAAW,CAACN,MAAM,CAAC,CAAA;;AAE5B;AACA,EAAA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACjB,MAAM,EAAEsB,CAAC,EAAE,EAAE;IACzC,MAAMC,KAAK,GAAGC,aAAa,CAACP,SAAS,CAACK,CAAC,CAAC,CAAC,CAAA;AACzC,IAAA,IAAI,CAACP,MAAM,CAACO,CAAC,CAAC,EAAE;AACd;MACAF,SAAS,IAAIG,KAAK,CAACvB,MAAM,CAAA;MACzBJ,SAAS,GAAG,EAAE,CAAA;AACd,MAAA,SAAA;AACF,KAAA;AACA;AACA,IAAA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACvB,MAAM,EAAEyB,CAAC,EAAE,EAAE;AACrCL,MAAAA,SAAS,EAAE,CAAA;MACX,MAAMvB,SAAS,GAAGkB,MAAM,CAACO,CAAC,CAAC,CAACG,CAAC,CAAC,CAAA;AAC9B;AACA,MAAA,IAAI5B,SAAS,IAAI6B,MAAM,CAACC,IAAI,CAAC9B,SAAS,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QAClD,IAAIL,eAAe,CAACC,SAAS,EAAEC,SAAS,EAAE,IAAI,CAAC,EAAE;UAC/CsB,WAAW,CAACS,IAAI,CAAC;AACfC,YAAAA,KAAK,EAAET,SAAS;YAChBU,GAAG,EAAEV,SAAS,GAAG,CAAC;AAClBW,YAAAA,KAAK,EAAElC,SAAAA;AACT,WAAC,CAAC,CAAA;AACJ,SAAC,MAAM;AACL;UACAsB,WAAW,CAACA,WAAW,CAACnB,MAAM,GAAG,CAAC,CAAC,CAAC8B,GAAG,EAAE,CAAA;AAC3C,SAAA;AACF,OAAA;AACAlC,MAAAA,SAAS,GAAGC,SAAS,IAAI,EAAE,CAAA;AAC7B,KAAA;AACF,GAAA;AACA,EAAA,OAAOsB,WAAW,CAAA;AACpB,EAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACaa,eAAe,GAAGA,CAC7BjB,MAAkC,EAClCC,IAAY,KACE;AACd,EAAA,IAAI,CAACiB,KAAK,CAACC,OAAO,CAACnB,MAAM,CAAC,EAAE;AAC1B;IACA,OAAOM,WAAW,CAACN,MAAM,CAAC,CAAA;AAC5B,GAAA;AACA,EAAA,MAAME,SAAS,GAAGD,IAAI,CAACE,KAAK,CAACiB,SAAS,CAAC;IACrCC,YAAuB,GAAG,EAAE,CAAA;EAC9B,IAAIhB,SAAS,GAAG,CAAC,CAAC;AAChBiB,IAAAA,UAAU,GAAG,CAAC,CAAA;AAChB;AACA,EAAA,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,CAACjB,MAAM,EAAEsB,CAAC,EAAE,EAAE;IACzC,MAAMC,KAAK,GAAGC,aAAa,CAACP,SAAS,CAACK,CAAC,CAAC,CAAC,CAAA;;AAEzC;AACA,IAAA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACvB,MAAM,EAAEyB,CAAC,EAAE,EAAE;AACrCL,MAAAA,SAAS,EAAE,CAAA;AACX;MACA,IACEL,MAAM,CAACsB,UAAU,CAAC,IAClBtB,MAAM,CAACsB,UAAU,CAAC,CAACR,KAAK,IAAIT,SAAS,IACrCA,SAAS,GAAGL,MAAM,CAACsB,UAAU,CAAC,CAACP,GAAG,EAClC;AACA;QACAM,YAAY,CAACd,CAAC,CAAC,GAAGc,YAAY,CAACd,CAAC,CAAC,IAAI,EAAE,CAAA;AACvC;AACAc,QAAAA,YAAY,CAACd,CAAC,CAAC,CAACG,CAAC,CAAC,GAAAa,cAAA,CAAA,EAAA,EAAQvB,MAAM,CAACsB,UAAU,CAAC,CAACN,KAAK,CAAE,CAAA;AACpD;QACA,IAAIX,SAAS,KAAKL,MAAM,CAACsB,UAAU,CAAC,CAACP,GAAG,GAAG,CAAC,EAAE;AAC5CO,UAAAA,UAAU,EAAE,CAAA;AACd,SAAA;AACF,OAAA;AACF,KAAA;AACF,GAAA;AACA,EAAA,OAAOD,YAAY,CAAA;AACrB;;;;"}