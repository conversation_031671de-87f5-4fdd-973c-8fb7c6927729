# AMARK 故障排除指南

## 页面空白问题解决方案

### 1. 检查服务器状态
- 确认开发服务器正在运行：`npm run dev`
- 访问测试页面：`http://localhost:5173/test.html`
- 如果测试页面正常，说明服务器工作正常

### 2. 检查浏览器控制台
1. 按 `F12` 打开开发者工具
2. 点击 `Console` 标签页
3. 查看是否有红色错误信息
4. 常见错误类型：
   - `Module not found` - 模块导入错误
   - `Syntax Error` - 语法错误
   - `Network Error` - 网络连接问题

### 3. 清除浏览器缓存
- 按 `Ctrl + F5` (Windows) 或 `Cmd + Shift + R` (Mac) 强制刷新
- 或者在开发者工具中右键刷新按钮选择"清空缓存并硬性重新加载"

### 4. 检查依赖安装
```bash
cd amark-annotation
npm install
npm run dev
```

### 5. 端口冲突解决
如果5173端口被占用，尝试其他端口：
```bash
npm run dev -- --port 3000
```

### 6. 重新安装依赖
```bash
cd amark-annotation
rm -rf node_modules
rm package-lock.json
npm install
npm run dev
```

### 7. 检查Node.js版本
确保使用Node.js 16或更高版本：
```bash
node --version
npm --version
```

## 当前应用状态

### ✅ 已修复的问题
- 简化了CSS样式，避免样式冲突
- 创建了最基础的React组件
- 添加了测试页面用于故障排除

### 🎯 当前功能
- 基础React应用框架
- 简单的用户界面
- 测试页面和故障排除工具

### 📝 下一步
如果基础应用正常显示，可以逐步添加功能：
1. Redux状态管理
2. 图像上传功能
3. 标注工具
4. 数据导出功能

## 联系支持
如果问题仍然存在，请提供：
1. 浏览器控制台的错误信息
2. 开发服务器的输出日志
3. 操作系统和浏览器版本
4. Node.js和npm版本
