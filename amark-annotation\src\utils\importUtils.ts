import * as XLSX from 'xlsx';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, Point, ImportOptions } from '../types';

// 解析Excel文件
export const parseExcelFile = async (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // 读取第一个工作表
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 转换为JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsArrayBuffer(file);
  });
};

// 解析CSV文件
export const parseCSVFile = async (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const workbook = XLSX.read(text, { type: 'string' });
        
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        resolve(jsonData);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsText(file, 'utf-8');
  });
};

// 解析JSON文件
export const parseJSONFile = async (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const jsonData = JSON.parse(text);
        resolve(jsonData);
      } catch (error) {
        reject(new Error('JSON格式错误'));
      }
    };
    
    reader.onerror = () => reject(new Error('文件读取失败'));
    reader.readAsText(file, 'utf-8');
  });
};

// 验证导入数据
export const validateImportData = (data: any[], options: ImportOptions) => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!Array.isArray(data) || data.length === 0) {
    errors.push('数据为空或格式不正确');
    return { errors, warnings, validRows: [] };
  }
  
  const validRows: any[] = [];
  const requiredFields = ['标注名称', '标注类型'];
  
  data.forEach((row, index) => {
    const rowErrors: string[] = [];
    
    // 检查必需字段
    requiredFields.forEach(field => {
      if (!row[field] || row[field].toString().trim() === '') {
        rowErrors.push(`第${index + 1}行缺少必需字段: ${field}`);
      }
    });
    
    // 验证标注类型
    const validTypes = ['point', 'line', 'rectangle', 'circle', 'polygon', 'freehand'];
    if (row['标注类型'] && !validTypes.includes(row['标注类型'])) {
      rowErrors.push(`第${index + 1}行标注类型无效: ${row['标注类型']}`);
    }
    
    // 验证坐标格式
    if (row['坐标']) {
      try {
        parseCoordinates(row['坐标']);
      } catch (error) {
        rowErrors.push(`第${index + 1}行坐标格式错误: ${row['坐标']}`);
      }
    }
    
    if (rowErrors.length === 0) {
      validRows.push(row);
    } else {
      errors.push(...rowErrors);
    }
  });
  
  return { errors, warnings, validRows };
};

// 解析坐标字符串
const parseCoordinates = (coordStr: string): Point[] => {
  if (!coordStr) return [];
  
  // 支持多种格式: "(x,y);(x,y)" 或 "x,y;x,y"
  const coordPairs = coordStr.split(';');
  const points: Point[] = [];
  
  coordPairs.forEach(pair => {
    const cleaned = pair.replace(/[()]/g, '').trim();
    const [x, y] = cleaned.split(',').map(v => parseFloat(v.trim()));
    
    if (!isNaN(x) && !isNaN(y)) {
      points.push({ x, y });
    } else {
      throw new Error(`无效的坐标对: ${pair}`);
    }
  });
  
  return points;
};

// 解析样式字符串
const parseStyle = (styleStr: string): Partial<MarkerStyle> => {
  const style: Partial<MarkerStyle> = {};
  
  if (!styleStr) return style;
  
  // 解析格式: "颜色:#ff0000;线宽:2;线型:solid"
  const stylePairs = styleStr.split(';');
  
  stylePairs.forEach(pair => {
    const [key, value] = pair.split(':').map(v => v.trim());
    
    switch (key) {
      case '颜色':
        style.color = value;
        break;
      case '线宽':
        style.lineWidth = parseFloat(value);
        break;
      case '线型':
        if (['solid', 'dashed', 'dotted'].includes(value)) {
          style.lineStyle = value as 'solid' | 'dashed' | 'dotted';
        }
        break;
    }
  });
  
  return style;
};

// 转换导入数据为标注对象
export const convertImportDataToMarkers = (
  data: any[], 
  options: ImportOptions,
  defaultStyle: MarkerStyle
): Marker[] => {
  const markers: Marker[] = [];
  
  data.forEach(row => {
    try {
      const now = new Date();
      
      const marker: Marker = {
        id: crypto.randomUUID(),
        name: row['标注名称'] || `标注_${Date.now()}`,
        type: row['标注类型'] as Marker['type'],
        points: parseCoordinates(row['坐标'] || ''),
        style: {
          ...defaultStyle,
          ...parseStyle(row['样式'] || '')
        },
        groupIds: [],
        tags: row['标签'] ? row['标签'].split(';').filter(Boolean) : [],
        visible: row['可见'] !== '否',
        locked: row['锁定'] === '是',
        metadata: {},
        createdAt: now,
        updatedAt: now,
      };
      
      // 添加自定义元数据
      Object.keys(row).forEach(key => {
        if (key.startsWith('元数据_')) {
          const metaKey = key.replace('元数据_', '');
          marker.metadata[metaKey] = row[key];
        }
      });
      
      markers.push(marker);
    } catch (error) {
      console.warn('跳过无效行:', row, error);
    }
  });
  
  return markers;
};

// 检测列映射
export const detectColumnMapping = (data: any[]): Record<string, string> => {
  if (!data || data.length === 0) return {};
  
  const firstRow = data[0];
  const mapping: Record<string, string> = {};
  
  // 常见的列名映射
  const commonMappings = {
    'name': ['标注名称', 'name', '名称', 'label'],
    'type': ['标注类型', 'type', '类型', 'marker_type'],
    'coordinates': ['坐标', 'coordinates', 'points', '点'],
    'visible': ['可见', 'visible', '显示'],
    'locked': ['锁定', 'locked', '锁定状态'],
    'style': ['样式', 'style', '风格'],
    'tags': ['标签', 'tags', '标记'],
  };
  
  Object.keys(firstRow).forEach(column => {
    const lowerColumn = column.toLowerCase();
    
    Object.entries(commonMappings).forEach(([standardKey, variants]) => {
      if (variants.some(variant => 
        lowerColumn.includes(variant.toLowerCase()) || 
        variant.toLowerCase().includes(lowerColumn)
      )) {
        mapping[standardKey] = column;
      }
    });
  });
  
  return mapping;
};

// 生成导入预览
export const generateImportPreview = (data: any[]) => {
  const preview = {
    totalRows: data.length,
    columns: data.length > 0 ? Object.keys(data[0]) : [],
    sampleData: data.slice(0, 5), // 前5行作为预览
    detectedMapping: detectColumnMapping(data),
  };
  
  return preview;
};
