import{Point as t}from"../../../Point.min.mjs";import{degreesToRadians as s}from"../radiansDegreesConversion.min.mjs";import{createVector as o}from"../vectors.min.mjs";class i{constructor(s){this.options=s,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new t(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new t(1/this.options.scaleX,1/this.options.scaleY):new t(1,1)}createSideVector(t,s){const i=o(t,s);return this.options.strokeUniform?i.multiply(this.scale):i}projectOrthogonally(t,s,o){return this.applySkew(t.add(this.calcOrthogonalProjection(t,s,o)))}isSkewed(){return 0!==this.options.skewX||0!==this.options.skewY}applySkew(o){const i=new t(o);return i.y+=i.x*Math.tan(s(this.options.skewY)),i.x+=i.y*Math.tan(s(this.options.skewX)),i}scaleUnitVector(t,s){return t.multiply(this.strokeUniformScalar).scalarMultiply(s)}}export{i as StrokeProjectionsBase};
//# sourceMappingURL=StrokeProjectionsBase.min.mjs.map
