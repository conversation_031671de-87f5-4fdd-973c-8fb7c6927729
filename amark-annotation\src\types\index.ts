// Core types for the image annotation software

export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface MarkerStyle {
  color: string;
  lineWidth: number;
  lineStyle: 'solid' | 'dashed' | 'dotted';
  fillColor?: string;
  fillOpacity?: number;
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: 'normal' | 'bold';
}

export interface Marker {
  id: string;
  type: 'point' | 'line' | 'rectangle' | 'circle' | 'polygon' | 'freehand';
  name: string;
  points: Point[];
  style: MarkerStyle;
  groupIds: string[];
  tags: string[];
  visible: boolean;
  locked: boolean;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface MarkerGroup {
  id: string;
  name: string;
  color: string;
  visible: boolean;
  parentId?: string;
  childIds: string[];
  markerIds: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ImageData {
  id: string;
  name: string;
  url: string;
  originalFile: File;
  size: Size;
  scale: number;
  rotation: number;
  position: Point;
  markers: Marker[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  images: ImageData[];
  groups: MarkerGroup[];
  settings: ProjectSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectSettings {
  defaultMarkerStyle: MarkerStyle;
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  maxUndoSteps: number;
  gridEnabled: boolean;
  gridSize: number;
  snapToGrid: boolean;
  measurementUnit: 'px' | 'mm' | 'cm' | 'in';
  measurementScale: number; // pixels per unit
}

export interface AppState {
  currentProject?: Project;
  selectedImageId?: string;
  selectedMarkerIds: string[];
  selectedGroupIds: string[];
  activeToolType: Marker['type'] | 'select';
  currentMarkerStyle: MarkerStyle;
  viewportState: {
    zoom: number;
    pan: Point;
    rotation: number;
  };
  ui: {
    sidebarVisible: boolean;
    propertiesPanelVisible: boolean;
    layersPanelVisible: boolean;
    toolbarVisible: boolean;
    theme: 'light' | 'dark';
  };
  history: {
    past: any[];
    present: any;
    future: any[];
  };
}

export interface ExportOptions {
  format: 'xlsx' | 'csv' | 'json';
  includeMetadata: boolean;
  includeTimestamps: boolean;
  includeStyles: boolean;
  selectedMarkersOnly: boolean;
  selectedGroupsOnly: boolean;
}

export interface ImportOptions {
  format: 'xlsx' | 'csv' | 'json';
  columnMapping: Record<string, string>;
  conflictResolution: 'skip' | 'replace' | 'rename' | 'merge';
  validateData: boolean;
}
