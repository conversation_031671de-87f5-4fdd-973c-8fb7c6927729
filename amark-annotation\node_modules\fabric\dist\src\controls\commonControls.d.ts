import { Control } from './Control';
export declare const createObjectDefaultControls: () => {
    ml: Control;
    mr: Control;
    mb: Control;
    mt: Control;
    tl: Control;
    tr: Control;
    bl: Control;
    br: Control;
    mtr: Control;
};
export declare const createResizeControls: () => {
    mr: Control;
    ml: Control;
};
export declare const createTextboxDefaultControls: () => {
    mr: Control;
    ml: Control;
    mb: Control;
    mt: Control;
    tl: Control;
    tr: Control;
    bl: Control;
    br: Control;
    mtr: Control;
};
//# sourceMappingURL=commonControls.d.ts.map