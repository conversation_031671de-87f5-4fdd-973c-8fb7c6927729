{"hash": "82749c68", "configHash": "9eec40dc", "lockfileHash": "63a64235", "browserHash": "63a6d948", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "76337752", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fa4ad896", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "429e1a49", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "950ecd35", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "bd8b5e10", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "eb9ba068", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "96f2da36", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "0d314a1c", "needsInterop": false}, "react-konva": {"src": "../../react-konva/es/ReactKonva.js", "file": "react-konva.js", "fileHash": "91f6d49b", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "98c63d17", "needsInterop": false}}, "chunks": {"chunk-A7ECLLTJ": {"file": "chunk-A7ECLLTJ.js"}, "chunk-UFORGWNI": {"file": "chunk-UFORGWNI.js"}, "chunk-HSUUC2QV": {"file": "chunk-HSUUC2QV.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}