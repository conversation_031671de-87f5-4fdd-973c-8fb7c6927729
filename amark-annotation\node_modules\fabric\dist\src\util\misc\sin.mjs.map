{"version": 3, "file": "sin.mjs", "sources": ["../../../../src/util/misc/sin.ts"], "sourcesContent": ["import type { TRadian } from '../../typedefs';\nimport { halfPI } from '../../constants';\n\n/**\n * Calculate the cos of an angle, avoiding returning floats for known results\n * This function is here just to avoid getting 0.999999999999999 when dealing\n * with numbers that are really 1 or 0.\n * @param {TRadian} angle the angle\n * @return {Number} the sin value for angle.\n */\nexport const sin = (angle: TRadian): number => {\n  if (angle === 0) {\n    return 0;\n  }\n  const angleSlice = angle / halfPI;\n  const value = Math.sign(angle);\n  switch (angleSlice) {\n    case 1:\n      return value;\n    case 2:\n      return 0;\n    case 3:\n      return -value;\n  }\n  return Math.sin(angle);\n};\n"], "names": ["sin", "angle", "angleSlice", "halfPI", "value", "Math", "sign"], "mappings": ";;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACaA,MAAAA,GAAG,GAAIC,KAAc,IAAa;EAC7C,IAAIA,KAAK,KAAK,CAAC,EAAE;AACf,IAAA,OAAO,CAAC,CAAA;AACV,GAAA;AACA,EAAA,MAAMC,UAAU,GAAGD,KAAK,GAAGE,MAAM,CAAA;AACjC,EAAA,MAAMC,KAAK,GAAGC,IAAI,CAACC,IAAI,CAACL,KAAK,CAAC,CAAA;AAC9B,EAAA,QAAQC,UAAU;AAChB,IAAA,KAAK,CAAC;AACJ,MAAA,OAAOE,KAAK,CAAA;AACd,IAAA,KAAK,CAAC;AACJ,MAAA,OAAO,CAAC,CAAA;AACV,IAAA,KAAK,CAAC;AACJ,MAAA,OAAO,CAACA,KAAK,CAAA;AACjB,GAAA;AACA,EAAA,OAAOC,IAAI,CAACL,GAAG,CAACC,KAAK,CAAC,CAAA;AACxB;;;;"}