{"version": 3, "file": "svgParsing.mjs", "sources": ["../../../../src/util/misc/svgParsing.ts"], "sourcesContent": ["import { Color } from '../../color/Color';\nimport { config } from '../../config';\nimport { DEFAULT_SVG_FONT_SIZE, FILL, NONE } from '../../constants';\nimport type { TBBox, SVGElementName, SupportedSVGUnit } from '../../typedefs';\nimport { toFixed } from './toFixed';\n\n/**\n * Returns array of attributes for given svg that fabric parses\n * @param {SVGElementName} type Type of svg element (eg. 'circle')\n * @return {Array} string names of supported attributes\n */\nexport const getSvgAttributes = (type: SVGElementName) => {\n  const commonAttributes = ['instantiated_by_use', 'style', 'id', 'class'];\n  switch (type) {\n    case 'linearGradient':\n      return commonAttributes.concat([\n        'x1',\n        'y1',\n        'x2',\n        'y2',\n        'gradientUnits',\n        'gradientTransform',\n      ]);\n    case 'radialGradient':\n      return commonAttributes.concat([\n        'gradientUnits',\n        'gradientTransform',\n        'cx',\n        'cy',\n        'r',\n        'fx',\n        'fy',\n        'fr',\n      ]);\n    case 'stop':\n      return commonAttributes.concat(['offset', 'stop-color', 'stop-opacity']);\n  }\n  return commonAttributes;\n};\n\n/**\n * Converts from attribute value to pixel value if applicable.\n * Returns converted pixels or original value not converted.\n * @param {string} value number to operate on\n * @param {number} fontSize\n * @return {number}\n */\nexport const parseUnit = (value: string, fontSize = DEFAULT_SVG_FONT_SIZE) => {\n  const unit = /\\D{0,2}$/.exec(value),\n    number = parseFloat(value);\n  const dpi = config.DPI;\n  switch (unit?.[0] as SupportedSVGUnit) {\n    case 'mm':\n      return (number * dpi) / 25.4;\n\n    case 'cm':\n      return (number * dpi) / 2.54;\n\n    case 'in':\n      return number * dpi;\n\n    case 'pt':\n      return (number * dpi) / 72; // or * 4 / 3\n\n    case 'pc':\n      return ((number * dpi) / 72) * 12; // or * 16\n\n    case 'em':\n      return number * fontSize;\n\n    default:\n      return number;\n  }\n};\n\nexport type MeetOrSlice = 'meet' | 'slice';\n\nexport type MinMidMax = 'Min' | 'Mid' | 'Max' | 'none';\n\nexport type TPreserveArParsed = {\n  meetOrSlice: MeetOrSlice;\n  alignX: MinMidMax;\n  alignY: MinMidMax;\n};\n\n// align can be either none or undefined or a combination of mid/max\nconst parseAlign = (align: string): MinMidMax[] => {\n  //divide align in alignX and alignY\n  if (align && align !== NONE) {\n    return [align.slice(1, 4) as MinMidMax, align.slice(5, 8) as MinMidMax];\n  } else if (align === NONE) {\n    return [align, align];\n  }\n  return ['Mid', 'Mid'];\n};\n\n/**\n * Parse preserveAspectRatio attribute from element\n * https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/preserveAspectRatio\n * @param {string} attribute to be parsed\n * @return {Object} an object containing align and meetOrSlice attribute\n */\nexport const parsePreserveAspectRatioAttribute = (\n  attribute: string,\n): TPreserveArParsed => {\n  const [firstPart, secondPart] = attribute.trim().split(' ') as [\n    MinMidMax,\n    MeetOrSlice | undefined,\n  ];\n  const [alignX, alignY] = parseAlign(firstPart);\n  return {\n    meetOrSlice: secondPart || 'meet',\n    alignX,\n    alignY,\n  };\n};\n\n/**\n * Adobe Illustrator (at least CS5) is unable to render rgba()-based fill values\n * we work around it by \"moving\" alpha channel into opacity attribute and setting fill's alpha to 1\n * @param prop\n * @param value\n * @param {boolean} inlineStyle The default is inline style, the separator used is \":\", The other is \"=\"\n * @returns\n */\nexport const colorPropToSVG = (\n  prop: string,\n  value?: any,\n  inlineStyle = true,\n) => {\n  let colorValue;\n  let opacityValue;\n  if (!value) {\n    colorValue = 'none';\n  } else if (value.toLive) {\n    colorValue = `url(#SVGID_${value.id})`;\n  } else {\n    const color = new Color(value),\n      opacity = color.getAlpha();\n\n    colorValue = color.toRgb();\n    if (opacity !== 1) {\n      opacityValue = opacity.toString();\n    }\n  }\n  if (inlineStyle) {\n    return `${prop}: ${colorValue}; ${\n      opacityValue ? `${prop}-opacity: ${opacityValue}; ` : ''\n    }`;\n  } else {\n    return `${prop}=\"${colorValue}\" ${\n      opacityValue ? `${prop}-opacity=\"${opacityValue}\" ` : ''\n    }`;\n  }\n};\n\nexport const createSVGRect = (\n  color: string,\n  { left, top, width, height }: TBBox,\n  precision = config.NUM_FRACTION_DIGITS,\n) => {\n  const svgColor = colorPropToSVG(FILL, color, false);\n  const [x, y, w, h] = [left, top, width, height].map((value) =>\n    toFixed(value, precision),\n  );\n  return `<rect ${svgColor} x=\"${x}\" y=\"${y}\" width=\"${w}\" height=\"${h}\"></rect>`;\n};\n"], "names": ["getSvgAttributes", "type", "commonAttributes", "concat", "parseUnit", "value", "fontSize", "arguments", "length", "undefined", "DEFAULT_SVG_FONT_SIZE", "unit", "exec", "number", "parseFloat", "dpi", "config", "DPI", "parseAlign", "align", "NONE", "slice", "parsePreserveAspectRatioAttribute", "attribute", "firstPart", "second<PERSON><PERSON>", "trim", "split", "alignX", "alignY", "meetOrSlice", "colorPropToSVG", "prop", "inlineStyle", "colorValue", "opacityValue", "toLive", "id", "color", "Color", "opacity", "get<PERSON><PERSON><PERSON>", "toRgb", "toString", "createSVGRect", "_ref", "left", "top", "width", "height", "precision", "NUM_FRACTION_DIGITS", "svgColor", "FILL", "x", "y", "w", "h", "map", "toFixed"], "mappings": ";;;;;AAMA;AACA;AACA;AACA;AACA;AACaA,MAAAA,gBAAgB,GAAIC,IAAoB,IAAK;EACxD,MAAMC,gBAAgB,GAAG,CAAC,qBAAqB,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;AACxE,EAAA,QAAQD,IAAI;AACV,IAAA,KAAK,gBAAgB;AACnB,MAAA,OAAOC,gBAAgB,CAACC,MAAM,CAAC,CAC7B,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,eAAe,EACf,mBAAmB,CACpB,CAAC,CAAA;AACJ,IAAA,KAAK,gBAAgB;MACnB,OAAOD,gBAAgB,CAACC,MAAM,CAAC,CAC7B,eAAe,EACf,mBAAmB,EACnB,IAAI,EACJ,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CAAC,CAAA;AACJ,IAAA,KAAK,MAAM;MACT,OAAOD,gBAAgB,CAACC,MAAM,CAAC,CAAC,QAAQ,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,CAAA;AAC5E,GAAA;AACA,EAAA,OAAOD,gBAAgB,CAAA;AACzB,EAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;MACaE,SAAS,GAAG,UAACC,KAAa,EAAuC;AAAA,EAAA,IAArCC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,qBAAqB,CAAA;AACvE,EAAA,MAAMC,IAAI,GAAG,UAAU,CAACC,IAAI,CAACP,KAAK,CAAC;AACjCQ,IAAAA,MAAM,GAAGC,UAAU,CAACT,KAAK,CAAC,CAAA;AAC5B,EAAA,MAAMU,GAAG,GAAGC,MAAM,CAACC,GAAG,CAAA;AACtB,EAAA,QAAQN,IAAI,KAAJA,IAAAA,IAAAA,IAAI,uBAAJA,IAAI,CAAG,CAAC,CAAC;AACf,IAAA,KAAK,IAAI;AACP,MAAA,OAAQE,MAAM,GAAGE,GAAG,GAAI,IAAI,CAAA;AAE9B,IAAA,KAAK,IAAI;AACP,MAAA,OAAQF,MAAM,GAAGE,GAAG,GAAI,IAAI,CAAA;AAE9B,IAAA,KAAK,IAAI;MACP,OAAOF,MAAM,GAAGE,GAAG,CAAA;AAErB,IAAA,KAAK,IAAI;AACP,MAAA,OAAQF,MAAM,GAAGE,GAAG,GAAI,EAAE,CAAA;AAAE;;AAE9B,IAAA,KAAK,IAAI;AACP,MAAA,OAASF,MAAM,GAAGE,GAAG,GAAI,EAAE,GAAI,EAAE,CAAA;AAAE;;AAErC,IAAA,KAAK,IAAI;MACP,OAAOF,MAAM,GAAGP,QAAQ,CAAA;AAE1B,IAAA;AACE,MAAA,OAAOO,MAAM,CAAA;AACjB,GAAA;AACF,EAAC;AAYD;AACA,MAAMK,UAAU,GAAIC,KAAa,IAAkB;AACjD;AACA,EAAA,IAAIA,KAAK,IAAIA,KAAK,KAAKC,IAAI,EAAE;AAC3B,IAAA,OAAO,CAACD,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAeF,KAAK,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAc,CAAA;AACzE,GAAC,MAAM,IAAIF,KAAK,KAAKC,IAAI,EAAE;AACzB,IAAA,OAAO,CAACD,KAAK,EAAEA,KAAK,CAAC,CAAA;AACvB,GAAA;AACA,EAAA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AACvB,CAAC,CAAA;;AAED;AACA;AACA;AACA;AACA;AACA;AACaG,MAAAA,iCAAiC,GAC5CC,SAAiB,IACK;AACtB,EAAA,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGF,SAAS,CAACG,IAAI,EAAE,CAACC,KAAK,CAAC,GAAG,CAGzD,CAAA;EACD,MAAM,CAACC,MAAM,EAAEC,MAAM,CAAC,GAAGX,UAAU,CAACM,SAAS,CAAC,CAAA;EAC9C,OAAO;IACLM,WAAW,EAAEL,UAAU,IAAI,MAAM;IACjCG,MAAM;AACNC,IAAAA,MAAAA;GACD,CAAA;AACH,EAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAME,cAAc,GAAG,UAC5BC,IAAY,EACZ3B,KAAW,EAER;AAAA,EAAA,IADH4B,WAAW,GAAA1B,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAG,IAAI,CAAA;AAElB,EAAA,IAAI2B,UAAU,CAAA;AACd,EAAA,IAAIC,YAAY,CAAA;EAChB,IAAI,CAAC9B,KAAK,EAAE;AACV6B,IAAAA,UAAU,GAAG,MAAM,CAAA;AACrB,GAAC,MAAM,IAAI7B,KAAK,CAAC+B,MAAM,EAAE;AACvBF,IAAAA,UAAU,iBAAA/B,MAAA,CAAiBE,KAAK,CAACgC,EAAE,EAAG,GAAA,CAAA,CAAA;AACxC,GAAC,MAAM;AACL,IAAA,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAClC,KAAK,CAAC;AAC5BmC,MAAAA,OAAO,GAAGF,KAAK,CAACG,QAAQ,EAAE,CAAA;AAE5BP,IAAAA,UAAU,GAAGI,KAAK,CAACI,KAAK,EAAE,CAAA;IAC1B,IAAIF,OAAO,KAAK,CAAC,EAAE;AACjBL,MAAAA,YAAY,GAAGK,OAAO,CAACG,QAAQ,EAAE,CAAA;AACnC,KAAA;AACF,GAAA;AACA,EAAA,IAAIV,WAAW,EAAE;IACf,OAAA9B,EAAAA,CAAAA,MAAA,CAAU6B,IAAI,EAAA,IAAA,CAAA,CAAA7B,MAAA,CAAK+B,UAAU,QAAA/B,MAAA,CAC3BgC,YAAY,GAAAhC,EAAAA,CAAAA,MAAA,CAAM6B,IAAI,EAAA,YAAA,CAAA,CAAA7B,MAAA,CAAagC,YAAY,UAAO,EAAE,CAAA,CAAA;AAE5D,GAAC,MAAM;IACL,OAAAhC,EAAAA,CAAAA,MAAA,CAAU6B,IAAI,EAAA,KAAA,CAAA,CAAA7B,MAAA,CAAK+B,UAAU,SAAA/B,MAAA,CAC3BgC,YAAY,GAAAhC,EAAAA,CAAAA,MAAA,CAAM6B,IAAI,EAAA,aAAA,CAAA,CAAA7B,MAAA,CAAagC,YAAY,WAAO,EAAE,CAAA,CAAA;AAE5D,GAAA;AACF,EAAC;AAEM,MAAMS,aAAa,GAAG,UAC3BN,KAAa,EAAAO,IAAA,EAGV;EAAA,IAFH;IAAEC,IAAI;IAAEC,GAAG;IAAEC,KAAK;AAAEC,IAAAA,MAAAA;AAAc,GAAC,GAAAJ,IAAA,CAAA;AAAA,EAAA,IACnCK,SAAS,GAAA3C,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAE,CAAAA,CAAAA,KAAAA,SAAA,GAAAF,SAAA,CAAGS,CAAAA,CAAAA,GAAAA,MAAM,CAACmC,mBAAmB,CAAA;EAEtC,MAAMC,QAAQ,GAAGrB,cAAc,CAACsB,IAAI,EAAEf,KAAK,EAAE,KAAK,CAAC,CAAA;AACnD,EAAA,MAAM,CAACgB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAACX,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,CAAC,CAACS,GAAG,CAAErD,KAAK,IACxDsD,OAAO,CAACtD,KAAK,EAAE6C,SAAS,CAC1B,CAAC,CAAA;AACD,EAAA,OAAA,QAAA,CAAA/C,MAAA,CAAgBiD,QAAQ,WAAAjD,MAAA,CAAOmD,CAAC,EAAAnD,SAAAA,CAAAA,CAAAA,MAAA,CAAQoD,CAAC,iBAAApD,MAAA,CAAYqD,CAAC,EAAArD,cAAAA,CAAAA,CAAAA,MAAA,CAAasD,CAAC,EAAA,YAAA,CAAA,CAAA;AACtE;;;;"}