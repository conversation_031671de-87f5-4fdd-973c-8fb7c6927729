.sidebar {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background-color: var(--bg-secondary);
}

.sidebar-section {
  margin-bottom: 24px;
}

.sidebar-section h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dropzone {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--bg-primary);
  margin-bottom: 16px;
}

.dropzone:hover,
.dropzone.active {
  border-color: var(--accent-color);
  background-color: var(--hover-color);
}

.dropzone svg {
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.dropzone p {
  margin: 0 0 4px 0;
  color: var(--text-primary);
  font-weight: 500;
}

.dropzone small {
  color: var(--text-secondary);
  font-size: 12px;
}

.image-list {
  max-height: 400px;
  overflow-y: auto;
}

.image-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
  background-color: var(--bg-primary);
  border: 1px solid transparent;
}

.image-item:hover {
  background-color: var(--hover-color);
}

.image-item.selected {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.image-item.selected .image-details,
.image-item.selected .marker-count {
  color: rgba(255, 255, 255, 0.8);
}

.image-thumbnail {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  background-color: var(--bg-tertiary);
}

.image-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-info {
  flex: 1;
  min-width: 0;
}

.image-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-details {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.marker-count {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.image-details-panel {
  background-color: var(--bg-primary);
  border-radius: 6px;
  padding: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-row span {
  color: var(--text-primary);
  font-weight: 400;
}

.empty-state {
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 20px;
  margin: 0;
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar,
.image-list::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.image-list::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.image-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.image-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
