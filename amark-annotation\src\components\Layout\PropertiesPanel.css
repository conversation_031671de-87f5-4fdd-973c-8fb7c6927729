.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.selection-info {
  font-size: 12px;
  color: var(--text-secondary);
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 8px;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  background-color: var(--hover-color);
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--accent-color);
  border-bottom-color: var(--accent-color);
  background-color: var(--bg-primary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.property-group label {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.property-group input,
.property-group select,
.property-group textarea {
  padding: 8px 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 13px;
}

.property-group input:focus,
.property-group select:focus,
.property-group textarea:focus {
  outline: none;
  border-color: var(--accent-color);
}

.property-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.color-input {
  height: 36px;
  padding: 4px;
  cursor: pointer;
}

.range-input {
  margin-bottom: 4px;
}

.value-display {
  font-size: 12px;
  color: var(--text-secondary);
  text-align: center;
}

.select-input {
  cursor: pointer;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-group span {
  font-size: 13px;
  color: var(--text-primary);
}

.coordinates {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.coordinate-pair {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 12px;
  color: var(--text-primary);
}

.coordinate-pair:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

.coordinate-pair span:first-child {
  color: var(--text-secondary);
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  padding: 8px;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  min-height: 40px;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--accent-color);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.empty-tags {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 12px;
}

.empty-state {
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 40px 20px;
  margin: 0;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.coordinates::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.coordinates::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.coordinates::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.coordinates::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
