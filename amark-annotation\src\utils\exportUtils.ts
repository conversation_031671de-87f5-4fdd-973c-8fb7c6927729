import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { Project, Marker, ExportOptions } from '../types';

// 导出为Excel格式
export const exportToExcel = (project: Project, options: ExportOptions) => {
  const workbook = XLSX.utils.book_new();
  
  // 收集所有标注数据
  const allMarkers: any[] = [];
  
  project.images.forEach(image => {
    let markers = image.markers;
    
    // 如果只导出选中的标注，需要过滤
    if (options.selectedMarkersOnly) {
      // 这里需要从外部传入选中的标注ID列表
      // markers = markers.filter(m => selectedMarkerIds.includes(m.id));
    }
    
    markers.forEach(marker => {
      const row: any = {
        '图像名称': image.name,
        '标注ID': marker.id,
        '标注名称': marker.name,
        '标注类型': marker.type,
        '可见': marker.visible ? '是' : '否',
        '锁定': marker.locked ? '是' : '否',
      };
      
      // 添加坐标信息
      marker.points.forEach((point, index) => {
        row[`点${index + 1}_X`] = point.x.toFixed(2);
        row[`点${index + 1}_Y`] = point.y.toFixed(2);
      });
      
      // 添加样式信息
      if (options.includeStyles) {
        row['颜色'] = marker.style.color;
        row['线宽'] = marker.style.lineWidth;
        row['线型'] = marker.style.lineStyle;
        row['填充颜色'] = marker.style.fillColor;
        row['填充透明度'] = marker.style.fillOpacity;
      }
      
      // 添加时间戳
      if (options.includeTimestamps) {
        row['创建时间'] = new Date(marker.createdAt).toLocaleString();
        row['修改时间'] = new Date(marker.updatedAt).toLocaleString();
      }
      
      // 添加元数据
      if (options.includeMetadata) {
        row['标签'] = marker.tags.join(', ');
        Object.entries(marker.metadata).forEach(([key, value]) => {
          row[`元数据_${key}`] = value;
        });
      }
      
      allMarkers.push(row);
    });
  });
  
  // 创建工作表
  const worksheet = XLSX.utils.json_to_sheet(allMarkers);
  XLSX.utils.book_append_sheet(workbook, worksheet, '标注数据');
  
  // 如果包含项目信息，创建项目信息工作表
  if (options.includeMetadata) {
    const projectInfo = [
      { '属性': '项目名称', '值': project.name },
      { '属性': '项目描述', '值': project.description || '' },
      { '属性': '创建时间', '值': new Date(project.createdAt).toLocaleString() },
      { '属性': '修改时间', '值': new Date(project.updatedAt).toLocaleString() },
      { '属性': '图像数量', '值': project.images.length },
      { '属性': '标注总数', '值': project.images.reduce((sum, img) => sum + img.markers.length, 0) },
    ];
    
    const projectSheet = XLSX.utils.json_to_sheet(projectInfo);
    XLSX.utils.book_append_sheet(workbook, projectSheet, '项目信息');
  }
  
  // 保存文件
  const fileName = `${project.name}_标注数据_${new Date().toISOString().split('T')[0]}.xlsx`;
  XLSX.writeFile(workbook, fileName);
};

// 导出为CSV格式
export const exportToCSV = (project: Project, options: ExportOptions) => {
  const allMarkers: any[] = [];
  
  project.images.forEach(image => {
    image.markers.forEach(marker => {
      const row: any = {
        '图像名称': image.name,
        '标注ID': marker.id,
        '标注名称': marker.name,
        '标注类型': marker.type,
        '坐标': marker.points.map(p => `(${p.x.toFixed(2)},${p.y.toFixed(2)})`).join(';'),
        '可见': marker.visible ? '是' : '否',
        '锁定': marker.locked ? '是' : '否',
      };
      
      if (options.includeStyles) {
        row['样式'] = `颜色:${marker.style.color};线宽:${marker.style.lineWidth};线型:${marker.style.lineStyle}`;
      }
      
      if (options.includeTimestamps) {
        row['创建时间'] = new Date(marker.createdAt).toLocaleString();
        row['修改时间'] = new Date(marker.updatedAt).toLocaleString();
      }
      
      if (options.includeMetadata) {
        row['标签'] = marker.tags.join(';');
      }
      
      allMarkers.push(row);
    });
  });
  
  // 转换为CSV
  const worksheet = XLSX.utils.json_to_sheet(allMarkers);
  const csv = XLSX.utils.sheet_to_csv(worksheet);
  
  // 保存文件
  const fileName = `${project.name}_标注数据_${new Date().toISOString().split('T')[0]}.csv`;
  const blob = new Blob(['\uFEFF' + csv], { type: 'text/csv;charset=utf-8;' }); // 添加BOM以支持中文
  saveAs(blob, fileName);
};

// 导出为JSON格式
export const exportToJSON = (project: Project, options: ExportOptions) => {
  const exportData: any = {
    project: {
      name: project.name,
      description: project.description,
      exportDate: new Date().toISOString(),
      exportOptions: options,
    },
    images: [],
  };
  
  project.images.forEach(image => {
    const imageData: any = {
      id: image.id,
      name: image.name,
      size: image.size,
      markers: [],
    };
    
    image.markers.forEach(marker => {
      const markerData: any = {
        id: marker.id,
        name: marker.name,
        type: marker.type,
        points: marker.points,
        visible: marker.visible,
        locked: marker.locked,
      };
      
      if (options.includeStyles) {
        markerData.style = marker.style;
      }
      
      if (options.includeTimestamps) {
        markerData.createdAt = marker.createdAt;
        markerData.updatedAt = marker.updatedAt;
      }
      
      if (options.includeMetadata) {
        markerData.tags = marker.tags;
        markerData.metadata = marker.metadata;
      }
      
      imageData.markers.push(markerData);
    });
    
    exportData.images.push(imageData);
  });
  
  // 保存文件
  const fileName = `${project.name}_标注数据_${new Date().toISOString().split('T')[0]}.json`;
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
  saveAs(blob, fileName);
};

// 生成导出预览
export const generateExportPreview = (project: Project, options: ExportOptions) => {
  const stats = {
    totalImages: project.images.length,
    totalMarkers: project.images.reduce((sum, img) => sum + img.markers.length, 0),
    markerTypes: {} as Record<string, number>,
    estimatedFileSize: 0,
  };
  
  // 统计标注类型
  project.images.forEach(image => {
    image.markers.forEach(marker => {
      stats.markerTypes[marker.type] = (stats.markerTypes[marker.type] || 0) + 1;
    });
  });
  
  // 估算文件大小（粗略估算）
  const avgRowSize = 200; // 每行大约200字节
  stats.estimatedFileSize = stats.totalMarkers * avgRowSize;
  
  return stats;
};
