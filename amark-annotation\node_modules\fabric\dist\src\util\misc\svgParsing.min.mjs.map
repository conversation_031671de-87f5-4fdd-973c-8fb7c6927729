{"version": 3, "file": "svgParsing.min.mjs", "sources": ["../../../../src/util/misc/svgParsing.ts"], "sourcesContent": ["import { Color } from '../../color/Color';\nimport { config } from '../../config';\nimport { DEFAULT_SVG_FONT_SIZE, FILL, NONE } from '../../constants';\nimport type { TBBox, SVGElementName, SupportedSVGUnit } from '../../typedefs';\nimport { toFixed } from './toFixed';\n\n/**\n * Returns array of attributes for given svg that fabric parses\n * @param {SVGElementName} type Type of svg element (eg. 'circle')\n * @return {Array} string names of supported attributes\n */\nexport const getSvgAttributes = (type: SVGElementName) => {\n  const commonAttributes = ['instantiated_by_use', 'style', 'id', 'class'];\n  switch (type) {\n    case 'linearGradient':\n      return commonAttributes.concat([\n        'x1',\n        'y1',\n        'x2',\n        'y2',\n        'gradientUnits',\n        'gradientTransform',\n      ]);\n    case 'radialGradient':\n      return commonAttributes.concat([\n        'gradientUnits',\n        'gradientTransform',\n        'cx',\n        'cy',\n        'r',\n        'fx',\n        'fy',\n        'fr',\n      ]);\n    case 'stop':\n      return commonAttributes.concat(['offset', 'stop-color', 'stop-opacity']);\n  }\n  return commonAttributes;\n};\n\n/**\n * Converts from attribute value to pixel value if applicable.\n * Returns converted pixels or original value not converted.\n * @param {string} value number to operate on\n * @param {number} fontSize\n * @return {number}\n */\nexport const parseUnit = (value: string, fontSize = DEFAULT_SVG_FONT_SIZE) => {\n  const unit = /\\D{0,2}$/.exec(value),\n    number = parseFloat(value);\n  const dpi = config.DPI;\n  switch (unit?.[0] as SupportedSVGUnit) {\n    case 'mm':\n      return (number * dpi) / 25.4;\n\n    case 'cm':\n      return (number * dpi) / 2.54;\n\n    case 'in':\n      return number * dpi;\n\n    case 'pt':\n      return (number * dpi) / 72; // or * 4 / 3\n\n    case 'pc':\n      return ((number * dpi) / 72) * 12; // or * 16\n\n    case 'em':\n      return number * fontSize;\n\n    default:\n      return number;\n  }\n};\n\nexport type MeetOrSlice = 'meet' | 'slice';\n\nexport type MinMidMax = 'Min' | 'Mid' | 'Max' | 'none';\n\nexport type TPreserveArParsed = {\n  meetOrSlice: MeetOrSlice;\n  alignX: MinMidMax;\n  alignY: MinMidMax;\n};\n\n// align can be either none or undefined or a combination of mid/max\nconst parseAlign = (align: string): MinMidMax[] => {\n  //divide align in alignX and alignY\n  if (align && align !== NONE) {\n    return [align.slice(1, 4) as MinMidMax, align.slice(5, 8) as MinMidMax];\n  } else if (align === NONE) {\n    return [align, align];\n  }\n  return ['Mid', 'Mid'];\n};\n\n/**\n * Parse preserveAspectRatio attribute from element\n * https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/preserveAspectRatio\n * @param {string} attribute to be parsed\n * @return {Object} an object containing align and meetOrSlice attribute\n */\nexport const parsePreserveAspectRatioAttribute = (\n  attribute: string,\n): TPreserveArParsed => {\n  const [firstPart, secondPart] = attribute.trim().split(' ') as [\n    MinMidMax,\n    MeetOrSlice | undefined,\n  ];\n  const [alignX, alignY] = parseAlign(firstPart);\n  return {\n    meetOrSlice: secondPart || 'meet',\n    alignX,\n    alignY,\n  };\n};\n\n/**\n * Adobe Illustrator (at least CS5) is unable to render rgba()-based fill values\n * we work around it by \"moving\" alpha channel into opacity attribute and setting fill's alpha to 1\n * @param prop\n * @param value\n * @param {boolean} inlineStyle The default is inline style, the separator used is \":\", The other is \"=\"\n * @returns\n */\nexport const colorPropToSVG = (\n  prop: string,\n  value?: any,\n  inlineStyle = true,\n) => {\n  let colorValue;\n  let opacityValue;\n  if (!value) {\n    colorValue = 'none';\n  } else if (value.toLive) {\n    colorValue = `url(#SVGID_${value.id})`;\n  } else {\n    const color = new Color(value),\n      opacity = color.getAlpha();\n\n    colorValue = color.toRgb();\n    if (opacity !== 1) {\n      opacityValue = opacity.toString();\n    }\n  }\n  if (inlineStyle) {\n    return `${prop}: ${colorValue}; ${\n      opacityValue ? `${prop}-opacity: ${opacityValue}; ` : ''\n    }`;\n  } else {\n    return `${prop}=\"${colorValue}\" ${\n      opacityValue ? `${prop}-opacity=\"${opacityValue}\" ` : ''\n    }`;\n  }\n};\n\nexport const createSVGRect = (\n  color: string,\n  { left, top, width, height }: TBBox,\n  precision = config.NUM_FRACTION_DIGITS,\n) => {\n  const svgColor = colorPropToSVG(FILL, color, false);\n  const [x, y, w, h] = [left, top, width, height].map((value) =>\n    toFixed(value, precision),\n  );\n  return `<rect ${svgColor} x=\"${x}\" y=\"${y}\" width=\"${w}\" height=\"${h}\"></rect>`;\n};\n"], "names": ["getSvgAttributes", "type", "commonAttributes", "concat", "parseUnit", "value", "fontSize", "arguments", "length", "undefined", "DEFAULT_SVG_FONT_SIZE", "unit", "exec", "number", "parseFloat", "dpi", "config", "DPI", "parsePreserveAspectRatioAttribute", "attribute", "firstPart", "second<PERSON><PERSON>", "trim", "split", "alignX", "alignY", "align", "NONE", "slice", "meetOrSlice", "colorPropToSVG", "prop", "colorValue", "opacityValue", "inlineStyle", "toLive", "id", "color", "Color", "opacity", "get<PERSON><PERSON><PERSON>", "toRgb", "toString", "createSVGRect", "_ref", "left", "top", "width", "height", "precision", "NUM_FRACTION_DIGITS", "svgColor", "FILL", "x", "y", "w", "h", "map", "toFixed"], "mappings": "gOAWaA,MAAAA,EAAoBC,IAC/B,MAAMC,EAAmB,CAAC,sBAAuB,QAAS,KAAM,SAChE,OAAQD,GACN,IAAK,iBACH,OAAOC,EAAiBC,OAAO,CAC7B,KACA,KACA,KACA,KACA,gBACA,sBAEJ,IAAK,iBACH,OAAOD,EAAiBC,OAAO,CAC7B,gBACA,oBACA,KACA,KACA,IACA,KACA,KACA,OAEJ,IAAK,OACH,OAAOD,EAAiBC,OAAO,CAAC,SAAU,aAAc,iBAE5D,OAAOD,CAAgB,EAUZE,EAAY,SAACC,GAAoD,IAArCC,EAAQC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAGG,EAClD,MAAMC,EAAO,WAAWC,KAAKP,GAC3BQ,EAASC,WAAWT,GAChBU,EAAMC,EAAOC,IACnB,OAAQN,eAAAA,EAAO,IACb,IAAK,KACH,OAAQE,EAASE,EAAO,KAE1B,IAAK,KACH,OAAQF,EAASE,EAAO,KAE1B,IAAK,KACH,OAAOF,EAASE,EAElB,IAAK,KACH,OAAQF,EAASE,EAAO,GAE1B,IAAK,KACH,OAASF,EAASE,EAAO,GAAM,GAEjC,IAAK,KACH,OAAOF,EAASP,EAElB,QACE,OAAOO,EAEb,EA6BaK,EACXC,IAEA,MAAOC,EAAWC,GAAcF,EAAUG,OAAOC,MAAM,MAIhDC,EAAQC,IAvBGC,EAuBkBN,IArBvBM,IAAUC,EACd,CAACD,EAAME,MAAM,EAAG,GAAiBF,EAAME,MAAM,EAAG,IAC9CF,IAAUC,EACZ,CAACD,EAAOA,GAEV,CAAC,MAAO,OAPGA,MAwBlB,MAAO,CACLG,YAAaR,GAAc,OAC3BG,SACAC,SACD,EAWUK,EAAiB,SAC5BC,EACA1B,GAEG,IACC2B,EACAC,EAHJC,IAAW3B,UAAAC,OAAA,QAAAC,IAAAF,UAAA,KAAAA,UAAA,GAIX,GAAKF,EAEE,GAAIA,EAAM8B,OACfH,gBAAU7B,OAAiBE,EAAM+B,GAAK,SACjC,CACL,MAAMC,EAAQ,IAAIC,EAAMjC,GACtBkC,EAAUF,EAAMG,WAElBR,EAAaK,EAAMI,QACH,IAAZF,IACFN,EAAeM,EAAQG,WAE3B,MAXEV,EAAa,OAYf,OAAIE,EACF/B,GAAAA,OAAU4B,EAAI,MAAA5B,OAAK6B,QAAU7B,OAC3B8B,EAAY9B,GAAAA,OAAM4B,EAAI,cAAA5B,OAAa8B,QAAmB,IAGxD9B,GAAAA,OAAU4B,EAAI,MAAA5B,OAAK6B,QAAU7B,OAC3B8B,EAAY9B,GAAAA,OAAM4B,EAAI,cAAA5B,OAAa8B,QAAmB,GAG5D,EAEaU,EAAgB,SAC3BN,EAAaO,GAGV,IAFHC,KAAEA,EAAIC,IAAEA,EAAGC,MAAEA,EAAKC,OAAEA,GAAeJ,EACnCK,EAAS1C,UAAAC,OAAAD,QAAAE,IAAAF,UAAAE,GAAAF,UAAGS,GAAAA,EAAOkC,oBAEnB,MAAMC,EAAWrB,EAAesB,EAAMf,GAAO,IACtCgB,EAAGC,EAAGC,EAAGC,GAAK,CAACX,EAAMC,EAAKC,EAAOC,GAAQS,KAAKpD,GACnDqD,EAAQrD,EAAO4C,KAEjB,MAAA,SAAA9C,OAAgBgD,UAAQhD,OAAOkD,EAAClD,SAAAA,OAAQmD,eAACnD,OAAYoD,EAACpD,cAAAA,OAAaqD,EAAC,YACtE"}