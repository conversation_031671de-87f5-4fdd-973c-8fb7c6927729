.layers-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-secondary);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.layer-stats {
  font-size: 12px;
  color: var(--text-secondary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.marker-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.marker-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: var(--bg-primary);
  border: 1px solid transparent;
}

.marker-item:hover {
  background-color: var(--hover-color);
}

.marker-item.selected {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.marker-item.selected .marker-type {
  color: rgba(255, 255, 255, 0.8);
}

.marker-item.hidden {
  opacity: 0.5;
}

.marker-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.marker-info {
  flex: 1;
  min-width: 0;
}

.marker-name {
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.marker-type {
  font-size: 11px;
  color: var(--text-secondary);
  text-transform: capitalize;
}

.marker-controls {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--text-secondary);
}

.control-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.marker-item.selected .control-button {
  color: rgba(255, 255, 255, 0.7);
}

.marker-item.selected .control-button:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

.control-button.active {
  color: var(--accent-color);
}

.marker-item.selected .control-button.active {
  color: white;
}

.marker-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.empty-state {
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 40px 20px;
  margin: 0;
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}
