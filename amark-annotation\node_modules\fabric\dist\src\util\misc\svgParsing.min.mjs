import{Color as t}from"../../color/Color.min.mjs";import{config as c}from"../../config.min.mjs";import{DEFAULT_SVG_FONT_SIZE as n,NONE as e,FILL as o}from"../../constants.min.mjs";import{toFixed as r}from"./toFixed.min.mjs";const a=t=>{const c=["instantiated_by_use","style","id","class"];switch(t){case"linearGradient":return c.concat(["x1","y1","x2","y2","gradientUnits","gradientTransform"]);case"radialGradient":return c.concat(["gradientUnits","gradientTransform","cx","cy","r","fx","fy","fr"]);case"stop":return c.concat(["offset","stop-color","stop-opacity"])}return c},i=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n;const o=/\D{0,2}$/.exec(t),r=parseFloat(t),a=c.DPI;switch(null==o?void 0:o[0]){case"mm":return r*a/25.4;case"cm":return r*a/2.54;case"in":return r*a;case"pt":return r*a/72;case"pc":return r*a/72*12;case"em":return r*e;default:return r}},s=t=>{const[c,n]=t.trim().split(" "),[o,r]=(a=c)&&a!==e?[a.slice(1,4),a.slice(5,8)]:a===e?[a,a]:["Mid","Mid"];var a;return{meetOrSlice:n||"meet",alignX:o,alignY:r}},l=function(c,n){let e,o,r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(n)if(n.toLive)e="url(#SVGID_".concat(n.id,")");else{const c=new t(n),r=c.getAlpha();e=c.toRgb(),1!==r&&(o=r.toString())}else e="none";return r?"".concat(c,": ").concat(e,"; ").concat(o?"".concat(c,"-opacity: ").concat(o,"; "):""):"".concat(c,'="').concat(e,'" ').concat(o?"".concat(c,'-opacity="').concat(o,'" '):"")},m=function(t,n){let{left:e,top:a,width:i,height:s}=n,m=arguments.length>2&&void 0!==arguments[2]?arguments[2]:c.NUM_FRACTION_DIGITS;const u=l(o,t,!1),[d,f,p,g]=[e,a,i,s].map((t=>r(t,m)));return"<rect ".concat(u,' x="').concat(d,'" y="').concat(f,'" width="').concat(p,'" height="').concat(g,'"></rect>')};export{l as colorPropToSVG,m as createSVGRect,a as getSvgAttributes,s as parsePreserveAspectRatioAttribute,i as parseUnit};
//# sourceMappingURL=svgParsing.min.mjs.map
