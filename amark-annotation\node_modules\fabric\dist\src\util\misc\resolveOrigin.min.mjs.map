{"version": 3, "file": "resolveOrigin.min.mjs", "sources": ["../../../../src/util/misc/resolveOrigin.ts"], "sourcesContent": ["import type { TOriginX, TOriginY } from '../../typedefs';\n\nconst originOffset = {\n  left: -0.5,\n  top: -0.5,\n  center: 0,\n  bottom: 0.5,\n  right: 0.5,\n};\n/**\n * Resolves origin value relative to center\n * @private\n * @param {TOriginX | TOriginY} originValue originX / originY\n * @returns number\n */\n\nexport const resolveOrigin = (\n  originValue: TOriginX | TOriginY | number,\n): number =>\n  typeof originValue === 'string'\n    ? originOffset[originValue]\n    : originValue - 0.5;\n"], "names": ["originOffset", "left", "top", "center", "bottom", "right", "<PERSON><PERSON><PERSON><PERSON>", "originValue"], "mappings": "AAEA,MAAMA,EAAe,CACnBC,MAAO,GACPC,KAAM,GACNC,OAAQ,EACRC,OAAQ,GACRC,MAAO,IASIC,EACXC,GAEuB,iBAAhBA,EACHP,EAAaO,GACbA,EAAc"}