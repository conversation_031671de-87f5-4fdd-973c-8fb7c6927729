import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { selectMarkers, updateMarker } from '../../store/slices/projectSlice';
import { Eye, EyeOff, Lock, Unlock, Trash2 } from 'lucide-react';
import './LayersPanel.css';

const LayersPanel: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject, selectedImageId, selectedMarkerIds } = useSelector((state: RootState) => state.project);

  if (!currentProject || !selectedImageId) {
    return (
      <div className="layers-panel">
        <div className="panel-header">
          <h3>图层</h3>
        </div>
        <div className="panel-content">
          <p className="empty-state">请选择一个图像</p>
        </div>
      </div>
    );
  }

  const selectedImage = currentProject.images.find(img => img.id === selectedImageId);
  if (!selectedImage) {
    return (
      <div className="layers-panel">
        <div className="panel-header">
          <h3>图层</h3>
        </div>
        <div className="panel-content">
          <p className="empty-state">图像未找到</p>
        </div>
      </div>
    );
  }

  const handleMarkerSelect = (markerId: string, multiSelect: boolean = false) => {
    if (multiSelect) {
      const newSelection = selectedMarkerIds.includes(markerId)
        ? selectedMarkerIds.filter(id => id !== markerId)
        : [...selectedMarkerIds, markerId];
      dispatch(selectMarkers(newSelection));
    } else {
      dispatch(selectMarkers([markerId]));
    }
  };

  const handleVisibilityToggle = (markerId: string) => {
    const marker = selectedImage.markers.find(m => m.id === markerId);
    if (marker) {
      dispatch(updateMarker({
        markerId,
        updates: { visible: !marker.visible }
      }));
    }
  };

  const handleLockToggle = (markerId: string) => {
    const marker = selectedImage.markers.find(m => m.id === markerId);
    if (marker) {
      dispatch(updateMarker({
        markerId,
        updates: { locked: !marker.locked }
      }));
    }
  };

  const getMarkerTypeIcon = (type: string) => {
    switch (type) {
      case 'point': return '📍';
      case 'line': return '📏';
      case 'rectangle': return '⬜';
      case 'circle': return '⭕';
      case 'polygon': return '🔷';
      case 'freehand': return '✏️';
      default: return '❓';
    }
  };

  return (
    <div className="layers-panel">
      <div className="panel-header">
        <h3>图层</h3>
        <div className="layer-stats">
          {selectedImage.markers.length} 个标注
        </div>
      </div>
      
      <div className="panel-content">
        {selectedImage.markers.length === 0 ? (
          <p className="empty-state">暂无标注</p>
        ) : (
          <div className="marker-list">
            {selectedImage.markers.map((marker) => (
              <div
                key={marker.id}
                className={`marker-item ${selectedMarkerIds.includes(marker.id) ? 'selected' : ''} ${!marker.visible ? 'hidden' : ''}`}
                onClick={(e) => handleMarkerSelect(marker.id, e.ctrlKey || e.metaKey)}
              >
                <div className="marker-icon">
                  {getMarkerTypeIcon(marker.type)}
                </div>
                
                <div className="marker-info">
                  <div className="marker-name">{marker.name}</div>
                  <div className="marker-type">{marker.type}</div>
                </div>
                
                <div className="marker-controls">
                  <button
                    className={`control-button ${marker.visible ? 'active' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleVisibilityToggle(marker.id);
                    }}
                    title={marker.visible ? '隐藏' : '显示'}
                  >
                    {marker.visible ? <Eye size={14} /> : <EyeOff size={14} />}
                  </button>
                  
                  <button
                    className={`control-button ${marker.locked ? 'active' : ''}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleLockToggle(marker.id);
                    }}
                    title={marker.locked ? '解锁' : '锁定'}
                  >
                    {marker.locked ? <Lock size={14} /> : <Unlock size={14} />}
                  </button>
                </div>
                
                <div 
                  className="marker-color" 
                  style={{ backgroundColor: marker.style.color }}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default LayersPanel;
