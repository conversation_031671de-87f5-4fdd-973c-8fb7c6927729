{"version": 3, "file": "StrokeProjectionsBase.mjs", "sources": ["../../../../../src/util/misc/projectStroke/StrokeProjectionsBase.ts"], "sourcesContent": ["import type { XY } from '../../../Point';\nimport { Point } from '../../../Point';\nimport { degreesToRadians } from '../radiansDegreesConversion';\nimport { createVector } from '../vectors';\nimport type { TProjectStrokeOnPointsOptions, TProjection } from './types';\n\n/**\n * @see https://github.com/fabricjs/fabric.js/pull/8344\n * @todo consider removing skewing from points before calculating stroke projection,\n * see https://github.com/fabricjs/fabric.js/commit/494a10ee2f8c2278ae9a55b20bf50cf6ee25b064#commitcomment-94751537\n */\nexport abstract class StrokeProjectionsBase {\n  declare options: TProjectStrokeOnPointsOptions;\n  declare scale: Point;\n  declare strokeUniformScalar: Point;\n  declare strokeProjectionMagnitude: number;\n\n  constructor(options: TProjectStrokeOnPointsOptions) {\n    this.options = options;\n    this.strokeProjectionMagnitude = this.options.strokeWidth / 2;\n    this.scale = new Point(this.options.scaleX, this.options.scaleY);\n    this.strokeUniformScalar = this.options.strokeUniform\n      ? new Point(1 / this.options.scaleX, 1 / this.options.scaleY)\n      : new Point(1, 1);\n  }\n\n  /**\n   * When the stroke is uniform, scaling affects the arrangement of points. So we must take it into account.\n   */\n  protected createSideVector(from: XY, to: XY) {\n    const v = createVector(from, to);\n    return this.options.strokeUniform ? v.multiply(this.scale) : v;\n  }\n\n  protected abstract calcOrthogonalProjection(\n    from: Point,\n    to: Point,\n    magnitude?: number,\n  ): Point;\n\n  protected projectOrthogonally(from: Point, to: Point, magnitude?: number) {\n    return this.applySkew(\n      from.add(this.calcOrthogonalProjection(from, to, magnitude)),\n    );\n  }\n\n  protected isSkewed() {\n    return this.options.skewX !== 0 || this.options.skewY !== 0;\n  }\n\n  protected applySkew(point: Point) {\n    const p = new Point(point);\n    // skewY must be applied before skewX as this distortion affects skewX calculation\n    p.y += p.x * Math.tan(degreesToRadians(this.options.skewY));\n    p.x += p.y * Math.tan(degreesToRadians(this.options.skewX));\n    return p;\n  }\n\n  protected scaleUnitVector(unitVector: Point, scalar: number) {\n    return unitVector.multiply(this.strokeUniformScalar).scalarMultiply(scalar);\n  }\n\n  protected abstract projectPoints(): Point[];\n\n  public abstract project(): TProjection[];\n}\n"], "names": ["StrokeProjectionsBase", "constructor", "options", "strokeProjectionMagnitude", "strokeWidth", "scale", "Point", "scaleX", "scaleY", "strokeUniformScalar", "strokeUniform", "createSideVector", "from", "to", "v", "createVector", "multiply", "projectOrthogonally", "magnitude", "applySkew", "add", "calcOrthogonalProjection", "isSkewed", "skewX", "skewY", "point", "p", "y", "x", "Math", "tan", "degreesToRadians", "scaleUnitVector", "unitVector", "scalar", "scalar<PERSON>ultiply"], "mappings": ";;;;AAMA;AACA;AACA;AACA;AACA;AACO,MAAeA,qBAAqB,CAAC;EAM1CC,WAAWA,CAACC,OAAsC,EAAE;IAClD,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAA;IACtB,IAAI,CAACC,yBAAyB,GAAG,IAAI,CAACD,OAAO,CAACE,WAAW,GAAG,CAAC,CAAA;AAC7D,IAAA,IAAI,CAACC,KAAK,GAAG,IAAIC,KAAK,CAAC,IAAI,CAACJ,OAAO,CAACK,MAAM,EAAE,IAAI,CAACL,OAAO,CAACM,MAAM,CAAC,CAAA;AAChE,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACP,OAAO,CAACQ,aAAa,GACjD,IAAIJ,KAAK,CAAC,CAAC,GAAG,IAAI,CAACJ,OAAO,CAACK,MAAM,EAAE,CAAC,GAAG,IAAI,CAACL,OAAO,CAACM,MAAM,CAAC,GAC3D,IAAIF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACrB,GAAA;;AAEA;AACF;AACA;AACYK,EAAAA,gBAAgBA,CAACC,IAAQ,EAAEC,EAAM,EAAE;AAC3C,IAAA,MAAMC,CAAC,GAAGC,YAAY,CAACH,IAAI,EAAEC,EAAE,CAAC,CAAA;AAChC,IAAA,OAAO,IAAI,CAACX,OAAO,CAACQ,aAAa,GAAGI,CAAC,CAACE,QAAQ,CAAC,IAAI,CAACX,KAAK,CAAC,GAAGS,CAAC,CAAA;AAChE,GAAA;AAQUG,EAAAA,mBAAmBA,CAACL,IAAW,EAAEC,EAAS,EAAEK,SAAkB,EAAE;AACxE,IAAA,OAAO,IAAI,CAACC,SAAS,CACnBP,IAAI,CAACQ,GAAG,CAAC,IAAI,CAACC,wBAAwB,CAACT,IAAI,EAAEC,EAAE,EAAEK,SAAS,CAAC,CAC7D,CAAC,CAAA;AACH,GAAA;AAEUI,EAAAA,QAAQA,GAAG;AACnB,IAAA,OAAO,IAAI,CAACpB,OAAO,CAACqB,KAAK,KAAK,CAAC,IAAI,IAAI,CAACrB,OAAO,CAACsB,KAAK,KAAK,CAAC,CAAA;AAC7D,GAAA;EAEUL,SAASA,CAACM,KAAY,EAAE;AAChC,IAAA,MAAMC,CAAC,GAAG,IAAIpB,KAAK,CAACmB,KAAK,CAAC,CAAA;AAC1B;AACAC,IAAAA,CAAC,CAACC,CAAC,IAAID,CAAC,CAACE,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACC,gBAAgB,CAAC,IAAI,CAAC7B,OAAO,CAACsB,KAAK,CAAC,CAAC,CAAA;AAC3DE,IAAAA,CAAC,CAACE,CAAC,IAAIF,CAAC,CAACC,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACC,gBAAgB,CAAC,IAAI,CAAC7B,OAAO,CAACqB,KAAK,CAAC,CAAC,CAAA;AAC3D,IAAA,OAAOG,CAAC,CAAA;AACV,GAAA;AAEUM,EAAAA,eAAeA,CAACC,UAAiB,EAAEC,MAAc,EAAE;AAC3D,IAAA,OAAOD,UAAU,CAACjB,QAAQ,CAAC,IAAI,CAACP,mBAAmB,CAAC,CAAC0B,cAAc,CAACD,MAAM,CAAC,CAAA;AAC7E,GAAA;AAKF;;;;"}