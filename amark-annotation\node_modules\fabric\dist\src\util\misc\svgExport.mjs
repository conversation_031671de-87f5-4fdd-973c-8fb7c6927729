import { toFixed } from './toFixed.mjs';
import { config } from '../../config.mjs';

/**
 * given an array of 6 number returns something like `"matrix(...numbers)"`
 * @param {TMat2D} transform an array with 6 numbers
 * @return {String} transform matrix for svg
 */
const matrixToSVG = transform => 'matrix(' + transform.map(value => toFixed(value, config.NUM_FRACTION_DIGITS)).join(' ') + ')';

export { matrixToSVG };
//# sourceMappingURL=svgExport.mjs.map
