{"version": 3, "file": "rotatePoint.min.mjs", "sources": ["../../../../src/util/misc/rotatePoint.ts"], "sourcesContent": ["import type { Point } from '../../Point';\nimport type { TRadian } from '../../typedefs';\n/**\n * Rotates `point` around `origin` with `radians`\n * @deprecated use the Point.rotate\n * @param {Point} origin The origin of the rotation\n * @param {Point} origin The origin of the rotation\n * @param {TRadian} radians The radians of the angle for the rotation\n * @return {Point} The new rotated point\n */\nexport const rotatePoint = (\n  point: Point,\n  origin: Point,\n  radians: TRadian,\n): Point => point.rotate(radians, origin);\n"], "names": ["rotatePoint", "point", "origin", "radians", "rotate"], "mappings": "MAUaA,EAAcA,CACzBC,EACAC,EACAC,IACUF,EAAMG,OAAOD,EAASD"}