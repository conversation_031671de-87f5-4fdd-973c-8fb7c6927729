{"version": 3, "file": "StrokeLineJoinProjections.mjs", "sources": ["../../../../../src/util/misc/projectStroke/StrokeLineJoinProjections.ts"], "sourcesContent": ["import type { XY } from '../../../Point';\nimport { Point } from '../../../Point';\nimport { halfPI, twoMathPi } from '../../../constants';\nimport type { TRadian } from '../../../typedefs';\nimport { degreesToRadians } from '../radiansDegreesConversion';\nimport {\n  calcAngleBetweenVectors,\n  calcVectorRotation,\n  crossProduct,\n  getOrthonormalVector,\n  getUnitVector,\n  isBetweenVectors,\n  magnitude,\n  rotateVector,\n} from '../vectors';\nimport { StrokeProjectionsBase } from './StrokeProjectionsBase';\nimport type { TProjection, TProjectStrokeOnPointsOptions } from './types';\n\nconst zeroVector = new Point();\n\n/**\n * class in charge of finding projections for each type of line join\n * @see {@link [Closed path projections at #8344](https://github.com/fabricjs/fabric.js/pull/8344#2-closed-path)}\n *\n * - MDN:\n *   - https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin\n *   - https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-linejoin\n * - Spec: https://svgwg.org/svg2-draft/painting.html#StrokeLinejoinProperty\n * - Playground to understand how the line joins works: https://hypertolosana.github.io/efficient-webgl-stroking/index.html\n * - View the calculated projections for each of the control points: https://codesandbox.io/s/project-stroke-points-with-context-to-trace-b8jc4j?file=/src/index.js\n *\n */\nexport class StrokeLineJoinProjections extends StrokeProjectionsBase {\n  /**\n   * The point being projected (the angle ∠BAC)\n   */\n  declare A: Point;\n  /**\n   * The point before A\n   */\n  declare B: Point;\n  /**\n   * The point after A\n   */\n  declare C: Point;\n  /**\n   * The AB vector\n   */\n  AB: Point;\n  /**\n   * The AC vector\n   */\n  AC: Point;\n  /**\n   * The angle of A (∠BAC)\n   */\n  alpha: TRadian;\n  /**\n   * The bisector of A (∠BAC)\n   */\n  bisector: Point;\n\n  static getOrthogonalRotationFactor(vector1: Point, vector2?: Point) {\n    const angle = vector2\n      ? calcAngleBetweenVectors(vector1, vector2)\n      : calcVectorRotation(vector1);\n    return Math.abs(angle) < halfPI ? -1 : 1;\n  }\n\n  constructor(A: XY, B: XY, C: XY, options: TProjectStrokeOnPointsOptions) {\n    super(options);\n    this.A = new Point(A);\n    this.B = new Point(B);\n    this.C = new Point(C);\n    this.AB = this.createSideVector(this.A, this.B);\n    this.AC = this.createSideVector(this.A, this.C);\n    this.alpha = calcAngleBetweenVectors(this.AB, this.AC);\n    this.bisector = getUnitVector(\n      // if AC is also the zero vector nothing will be projected\n      // in that case the next point will handle the projection\n      rotateVector(this.AB.eq(zeroVector) ? this.AC : this.AB, this.alpha / 2),\n    );\n  }\n\n  calcOrthogonalProjection(\n    from: Point,\n    to: Point,\n    magnitude: number = this.strokeProjectionMagnitude,\n  ) {\n    const vector = this.createSideVector(from, to);\n    const orthogonalProjection = getOrthonormalVector(vector);\n    const correctSide = StrokeLineJoinProjections.getOrthogonalRotationFactor(\n      orthogonalProjection,\n      this.bisector,\n    );\n    return this.scaleUnitVector(orthogonalProjection, magnitude * correctSide);\n  }\n\n  /**\n   * BEVEL\n   * Calculation: the projection points are formed by the vector orthogonal to the vertex.\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#2-2-bevel\n   */\n  projectBevel() {\n    const projections: Point[] = [];\n    // if `alpha` equals 0 or 2*PI, the projections are the same for `B` and `C`\n    (this.alpha % twoMathPi === 0 ? [this.B] : [this.B, this.C]).forEach(\n      (to) => {\n        projections.push(this.projectOrthogonally(this.A, to));\n        projections.push(\n          this.projectOrthogonally(this.A, to, -this.strokeProjectionMagnitude),\n        );\n      },\n    );\n    return projections;\n  }\n\n  /**\n   * MITER\n   * Calculation: the corner is formed by extending the outer edges of the stroke\n   * at the tangents of the path segments until they intersect.\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#2-1-miter\n   */\n  projectMiter() {\n    const projections: Point[] = [],\n      alpha = Math.abs(this.alpha),\n      hypotUnitScalar = 1 / Math.sin(alpha / 2),\n      miterVector = this.scaleUnitVector(\n        this.bisector,\n        -this.strokeProjectionMagnitude * hypotUnitScalar,\n      );\n\n    // When two line segments meet at a sharp angle, it is possible for the join to extend,\n    // far beyond the thickness of the line stroking the path. The stroke-miterlimit imposes\n    // a limit on the extent of the line join.\n    // MDN: https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-miterlimit\n    // When the stroke is uniform, scaling changes the arrangement of points, this changes the miter-limit\n    const strokeMiterLimit = this.options.strokeUniform\n      ? magnitude(\n          this.scaleUnitVector(this.bisector, this.options.strokeMiterLimit),\n        )\n      : this.options.strokeMiterLimit;\n\n    if (\n      magnitude(miterVector) / this.strokeProjectionMagnitude <=\n      strokeMiterLimit\n    ) {\n      projections.push(this.applySkew(this.A.add(miterVector)));\n    }\n    /* when the miter-limit is reached, the stroke line join becomes of type bevel.\n      We always need two orthogonal projections which are basically bevel-type projections,\n      so regardless of whether the miter-limit was reached or not, we include these projections.\n    */\n    projections.push(...this.projectBevel());\n\n    return projections;\n  }\n\n  /**\n   * ROUND (without skew)\n   * Calculation: the projections are the two vectors parallel to X and Y axes\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#2-3-1-round-without-skew\n   */\n  private projectRoundNoSkew(startCircle: Point, endCircle: Point) {\n    const projections: Point[] = [],\n      // correctSide is used to only consider projecting for the outer side\n      correctSide = new Point(\n        StrokeLineJoinProjections.getOrthogonalRotationFactor(this.bisector),\n        StrokeLineJoinProjections.getOrthogonalRotationFactor(\n          new Point(this.bisector.y, this.bisector.x),\n        ),\n      ),\n      radiusOnAxisX = new Point(1, 0)\n        .scalarMultiply(this.strokeProjectionMagnitude)\n        .multiply(this.strokeUniformScalar)\n        .multiply(correctSide),\n      radiusOnAxisY = new Point(0, 1)\n        .scalarMultiply(this.strokeProjectionMagnitude)\n        .multiply(this.strokeUniformScalar)\n        .multiply(correctSide);\n\n    [radiusOnAxisX, radiusOnAxisY].forEach((vector) => {\n      if (isBetweenVectors(vector, startCircle, endCircle)) {\n        projections.push(this.A.add(vector));\n      }\n    });\n    return projections;\n  }\n\n  /**\n   * ROUND (with skew)\n   * Calculation: the projections are the points furthest from the vertex in\n   * the direction of the X and Y axes after distortion.\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#2-3-2-round-skew\n   */\n  private projectRoundWithSkew(startCircle: Point, endCircle: Point) {\n    const projections: Point[] = [];\n\n    const { skewX, skewY, scaleX, scaleY, strokeUniform } = this.options,\n      shearing = new Point(\n        Math.tan(degreesToRadians(skewX)),\n        Math.tan(degreesToRadians(skewY)),\n      );\n    // The points furthest from the vertex in the direction of the X and Y axes after distortion\n    const circleRadius = this.strokeProjectionMagnitude,\n      newY = strokeUniform\n        ? circleRadius /\n          scaleY /\n          Math.sqrt(1 / scaleY ** 2 + (1 / scaleX ** 2) * shearing.y ** 2)\n        : circleRadius / Math.sqrt(1 + shearing.y ** 2),\n      furthestY = new Point(\n        // Safe guard due to floating point precision. In some situations the square root\n        // was returning NaN because of a negative number close to zero.\n        Math.sqrt(Math.max(circleRadius ** 2 - newY ** 2, 0)),\n        newY,\n      ),\n      newX = strokeUniform\n        ? circleRadius /\n          Math.sqrt(\n            1 +\n              (shearing.x ** 2 * (1 / scaleY) ** 2) /\n                (1 / scaleX + (1 / scaleX) * shearing.x * shearing.y) ** 2,\n          )\n        : circleRadius /\n          Math.sqrt(1 + shearing.x ** 2 / (1 + shearing.x * shearing.y) ** 2),\n      furthestX = new Point(\n        newX,\n        Math.sqrt(Math.max(circleRadius ** 2 - newX ** 2, 0)),\n      );\n\n    [\n      furthestX,\n      furthestX.scalarMultiply(-1),\n      furthestY,\n      furthestY.scalarMultiply(-1),\n    ]\n      // We need to skew the vector here as this information is used to check if\n      // it is between the start and end of the circle segment\n      .map((vector) =>\n        this.applySkew(\n          strokeUniform ? vector.multiply(this.strokeUniformScalar) : vector,\n        ),\n      )\n      .forEach((vector) => {\n        if (isBetweenVectors(vector, startCircle, endCircle)) {\n          projections.push(this.applySkew(this.A).add(vector));\n        }\n      });\n\n    return projections;\n  }\n\n  projectRound() {\n    const projections: Point[] = [];\n    /* Include the start and end points of the circle segment, so that only\n      the projections contained within it are included */\n    // add the orthogonal projections (start and end points of circle segment)\n    projections.push(...this.projectBevel());\n    // let's determines which one of the orthogonal projection is the beginning and end of the circle segment.\n    // when `alpha` equals 0 or 2*PI, we have a straight line, so the way to find the start/end is different.\n    const isStraightLine = this.alpha % twoMathPi === 0,\n      // change the origin of the projections to point A\n      // so that the cross product calculation is correct\n      newOrigin = this.applySkew(this.A),\n      proj0 = projections[isStraightLine ? 0 : 2].subtract(newOrigin),\n      proj1 = projections[isStraightLine ? 1 : 0].subtract(newOrigin),\n      // when `isStraightLine` === true, we compare with the vector opposite AB, otherwise we compare with the bisector.\n      comparisonVector = isStraightLine\n        ? this.applySkew(this.AB.scalarMultiply(-1))\n        : this.applySkew(\n            this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1),\n          ),\n      // the beginning of the circle segment is always to the right of the comparison vector (cross product > 0)\n      isProj0Start = crossProduct(proj0, comparisonVector) > 0,\n      startCircle = isProj0Start ? proj0 : proj1,\n      endCircle = isProj0Start ? proj1 : proj0;\n    if (!this.isSkewed()) {\n      projections.push(...this.projectRoundNoSkew(startCircle, endCircle));\n    } else {\n      projections.push(...this.projectRoundWithSkew(startCircle, endCircle));\n    }\n    return projections;\n  }\n\n  /**\n   * Project stroke width on points returning projections for each point as follows:\n   * - `miter`: 1 point corresponding to the outer boundary. If the miter limit is exceeded, it will be 2 points (becomes bevel)\n   * - `bevel`: 2 points corresponding to the bevel possible boundaries, orthogonal to the stroke.\n   * - `round`: same as `bevel` when it has no skew, with skew are 4 points.\n   */\n  protected projectPoints() {\n    switch (this.options.strokeLineJoin) {\n      case 'miter':\n        return this.projectMiter();\n      case 'round':\n        return this.projectRound();\n      default:\n        return this.projectBevel();\n    }\n  }\n\n  public project(): TProjection[] {\n    return this.projectPoints().map((point) => ({\n      originPoint: this.A,\n      projectedPoint: point,\n      angle: this.alpha,\n      bisector: this.bisector,\n    }));\n  }\n}\n"], "names": ["zeroVector", "Point", "StrokeLineJoinProjections", "StrokeProjectionsBase", "getOrthogonalRotationFactor", "vector1", "vector2", "angle", "calcAngleBetweenVectors", "calcVectorRotation", "Math", "abs", "halfPI", "constructor", "A", "B", "C", "options", "_defineProperty", "AB", "createSideVector", "AC", "alpha", "bisector", "getUnitVector", "rotateVector", "eq", "calcOrthogonalProjection", "from", "to", "magnitude", "arguments", "length", "undefined", "strokeProjectionMagnitude", "vector", "orthogonalProjection", "getOrthonormalVector", "correctSide", "scaleUnitVector", "projectBevel", "projections", "twoMathPi", "for<PERSON>ach", "push", "projectOrthogonally", "projectMiter", "hypotUnitScalar", "sin", "miterVector", "strokeMiterLimit", "strokeUniform", "applySkew", "add", "projectRoundNoSkew", "startCircle", "endCircle", "y", "x", "radiusOnAxisX", "scalar<PERSON>ultiply", "multiply", "strokeUniformScalar", "radiusOnAxisY", "isBetweenVectors", "projectRoundWithSkew", "skewX", "skewY", "scaleX", "scaleY", "shearing", "tan", "degreesToRadians", "circleRadius", "newY", "sqrt", "furthestY", "max", "newX", "furthestX", "map", "projectRound", "isStraightLine", "new<PERSON><PERSON><PERSON>", "proj0", "subtract", "proj1", "comparisonVector", "isProj0Start", "crossProduct", "isSkewed", "projectPoints", "stroke<PERSON><PERSON><PERSON><PERSON>n", "project", "point", "originPoint", "projectedPoint"], "mappings": ";;;;;;;AAkBA,MAAMA,UAAU,GAAG,IAAIC,KAAK,EAAE,CAAA;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,yBAAyB,SAASC,qBAAqB,CAAC;AA8BnE,EAAA,OAAOC,2BAA2BA,CAACC,OAAc,EAAEC,OAAe,EAAE;AAClE,IAAA,MAAMC,KAAK,GAAGD,OAAO,GACjBE,uBAAuB,CAACH,OAAO,EAAEC,OAAO,CAAC,GACzCG,kBAAkB,CAACJ,OAAO,CAAC,CAAA;AAC/B,IAAA,OAAOK,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC,GAAGK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;AAC1C,GAAA;EAEAC,WAAWA,CAACC,CAAK,EAAEC,CAAK,EAAEC,CAAK,EAAEC,OAAsC,EAAE;IACvE,KAAK,CAACA,OAAO,CAAC,CAAA;AArChB;AACF;AACA;AAEE;AACF;AACA;AAEE;AACF;AACA;AAEE;AACF;AACA;IAFEC,eAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAIA;AACF;AACA;IAFEA,eAAA,CAAA,IAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAIA;AACF;AACA;IAFEA,eAAA,CAAA,IAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAIA;AACF;AACA;IAFEA,eAAA,CAAA,IAAA,EAAA,UAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAcE,IAAA,IAAI,CAACJ,CAAC,GAAG,IAAIb,KAAK,CAACa,CAAC,CAAC,CAAA;AACrB,IAAA,IAAI,CAACC,CAAC,GAAG,IAAId,KAAK,CAACc,CAAC,CAAC,CAAA;AACrB,IAAA,IAAI,CAACC,CAAC,GAAG,IAAIf,KAAK,CAACe,CAAC,CAAC,CAAA;AACrB,IAAA,IAAI,CAACG,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACN,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,CAAA;AAC/C,IAAA,IAAI,CAACM,EAAE,GAAG,IAAI,CAACD,gBAAgB,CAAC,IAAI,CAACN,CAAC,EAAE,IAAI,CAACE,CAAC,CAAC,CAAA;AAC/C,IAAA,IAAI,CAACM,KAAK,GAAGd,uBAAuB,CAAC,IAAI,CAACW,EAAE,EAAE,IAAI,CAACE,EAAE,CAAC,CAAA;IACtD,IAAI,CAACE,QAAQ,GAAGC,aAAa;AAC3B;AACA;IACAC,YAAY,CAAC,IAAI,CAACN,EAAE,CAACO,EAAE,CAAC1B,UAAU,CAAC,GAAG,IAAI,CAACqB,EAAE,GAAG,IAAI,CAACF,EAAE,EAAE,IAAI,CAACG,KAAK,GAAG,CAAC,CACzE,CAAC,CAAA;AACH,GAAA;AAEAK,EAAAA,wBAAwBA,CACtBC,IAAW,EACXC,EAAS,EAET;AAAA,IAAA,IADAC,SAAiB,GAAAC,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAE,CAAAA,CAAAA,KAAAA,SAAA,GAAAF,SAAA,CAAG,CAAA,CAAA,GAAA,IAAI,CAACG,yBAAyB,CAAA;IAElD,MAAMC,MAAM,GAAG,IAAI,CAACf,gBAAgB,CAACQ,IAAI,EAAEC,EAAE,CAAC,CAAA;AAC9C,IAAA,MAAMO,oBAAoB,GAAGC,oBAAoB,CAACF,MAAM,CAAC,CAAA;IACzD,MAAMG,WAAW,GAAGpC,yBAAyB,CAACE,2BAA2B,CACvEgC,oBAAoB,EACpB,IAAI,CAACb,QACP,CAAC,CAAA;IACD,OAAO,IAAI,CAACgB,eAAe,CAACH,oBAAoB,EAAEN,SAAS,GAAGQ,WAAW,CAAC,CAAA;AAC5E,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEE,EAAAA,YAAYA,GAAG;IACb,MAAMC,WAAoB,GAAG,EAAE,CAAA;AAC/B;IACA,CAAC,IAAI,CAACnB,KAAK,GAAGoB,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC3B,CAAC,CAAC,GAAG,CAAC,IAAI,CAACA,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,EAAE2B,OAAO,CACjEd,EAAE,IAAK;AACNY,MAAAA,WAAW,CAACG,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC/B,CAAC,EAAEe,EAAE,CAAC,CAAC,CAAA;AACtDY,MAAAA,WAAW,CAACG,IAAI,CACd,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAAC/B,CAAC,EAAEe,EAAE,EAAE,CAAC,IAAI,CAACK,yBAAyB,CACtE,CAAC,CAAA;AACH,KACF,CAAC,CAAA;AACD,IAAA,OAAOO,WAAW,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACEK,EAAAA,YAAYA,GAAG;IACb,MAAML,WAAoB,GAAG,EAAE;MAC7BnB,KAAK,GAAGZ,IAAI,CAACC,GAAG,CAAC,IAAI,CAACW,KAAK,CAAC;MAC5ByB,eAAe,GAAG,CAAC,GAAGrC,IAAI,CAACsC,GAAG,CAAC1B,KAAK,GAAG,CAAC,CAAC;AACzC2B,MAAAA,WAAW,GAAG,IAAI,CAACV,eAAe,CAChC,IAAI,CAAChB,QAAQ,EACb,CAAC,IAAI,CAACW,yBAAyB,GAAGa,eACpC,CAAC,CAAA;;AAEH;AACA;AACA;AACA;AACA;AACA,IAAA,MAAMG,gBAAgB,GAAG,IAAI,CAACjC,OAAO,CAACkC,aAAa,GAC/CrB,SAAS,CACP,IAAI,CAACS,eAAe,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACN,OAAO,CAACiC,gBAAgB,CACnE,CAAC,GACD,IAAI,CAACjC,OAAO,CAACiC,gBAAgB,CAAA;IAEjC,IACEpB,SAAS,CAACmB,WAAW,CAAC,GAAG,IAAI,CAACf,yBAAyB,IACvDgB,gBAAgB,EAChB;AACAT,MAAAA,WAAW,CAACG,IAAI,CAAC,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACtC,CAAC,CAACuC,GAAG,CAACJ,WAAW,CAAC,CAAC,CAAC,CAAA;AAC3D,KAAA;AACA;AACJ;AACA;AACA;IACIR,WAAW,CAACG,IAAI,CAAC,GAAG,IAAI,CAACJ,YAAY,EAAE,CAAC,CAAA;AAExC,IAAA,OAAOC,WAAW,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACUa,EAAAA,kBAAkBA,CAACC,WAAkB,EAAEC,SAAgB,EAAE;IAC/D,MAAMf,WAAoB,GAAG,EAAE;AAC7B;AACAH,MAAAA,WAAW,GAAG,IAAIrC,KAAK,CACrBC,yBAAyB,CAACE,2BAA2B,CAAC,IAAI,CAACmB,QAAQ,CAAC,EACpErB,yBAAyB,CAACE,2BAA2B,CACnD,IAAIH,KAAK,CAAC,IAAI,CAACsB,QAAQ,CAACkC,CAAC,EAAE,IAAI,CAAClC,QAAQ,CAACmC,CAAC,CAC5C,CACF,CAAC;MACDC,aAAa,GAAG,IAAI1D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAC5B2D,cAAc,CAAC,IAAI,CAAC1B,yBAAyB,CAAC,CAC9C2B,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAClCD,QAAQ,CAACvB,WAAW,CAAC;MACxByB,aAAa,GAAG,IAAI9D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAC5B2D,cAAc,CAAC,IAAI,CAAC1B,yBAAyB,CAAC,CAC9C2B,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAClCD,QAAQ,CAACvB,WAAW,CAAC,CAAA;IAE1B,CAACqB,aAAa,EAAEI,aAAa,CAAC,CAACpB,OAAO,CAAER,MAAM,IAAK;MACjD,IAAI6B,gBAAgB,CAAC7B,MAAM,EAAEoB,WAAW,EAAEC,SAAS,CAAC,EAAE;QACpDf,WAAW,CAACG,IAAI,CAAC,IAAI,CAAC9B,CAAC,CAACuC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAAA;AACtC,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAOM,WAAW,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACUwB,EAAAA,oBAAoBA,CAACV,WAAkB,EAAEC,SAAgB,EAAE;IACjE,MAAMf,WAAoB,GAAG,EAAE,CAAA;IAE/B,MAAM;QAAEyB,KAAK;QAAEC,KAAK;QAAEC,MAAM;QAAEC,MAAM;AAAElB,QAAAA,aAAAA;OAAe,GAAG,IAAI,CAAClC,OAAO;MAClEqD,QAAQ,GAAG,IAAIrE,KAAK,CAClBS,IAAI,CAAC6D,GAAG,CAACC,gBAAgB,CAACN,KAAK,CAAC,CAAC,EACjCxD,IAAI,CAAC6D,GAAG,CAACC,gBAAgB,CAACL,KAAK,CAAC,CAClC,CAAC,CAAA;AACH;AACA,IAAA,MAAMM,YAAY,GAAG,IAAI,CAACvC,yBAAyB;AACjDwC,MAAAA,IAAI,GAAGvB,aAAa,GAChBsB,YAAY,GACZJ,MAAM,GACN3D,IAAI,CAACiE,IAAI,CAAC,CAAC,GAAGN,MAAM,IAAI,CAAC,GAAI,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAIE,QAAQ,CAACb,CAAC,IAAI,CAAC,CAAC,GAChEgB,YAAY,GAAG/D,IAAI,CAACiE,IAAI,CAAC,CAAC,GAAGL,QAAQ,CAACb,CAAC,IAAI,CAAC,CAAC;MACjDmB,SAAS,GAAG,IAAI3E,KAAK;AACnB;AACA;MACAS,IAAI,CAACiE,IAAI,CAACjE,IAAI,CAACmE,GAAG,CAACJ,YAAY,IAAI,CAAC,GAAGC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EACrDA,IACF,CAAC;AACDI,MAAAA,IAAI,GAAG3B,aAAa,GAChBsB,YAAY,GACZ/D,IAAI,CAACiE,IAAI,CACP,CAAC,GACEL,QAAQ,CAACZ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGW,MAAM,KAAK,CAAC,GAClC,CAAC,CAAC,GAAGD,MAAM,GAAI,CAAC,GAAGA,MAAM,GAAIE,QAAQ,CAACZ,CAAC,GAAGY,QAAQ,CAACb,CAAC,KAAK,CAC/D,CAAC,GACDgB,YAAY,GACZ/D,IAAI,CAACiE,IAAI,CAAC,CAAC,GAAGL,QAAQ,CAACZ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAGY,QAAQ,CAACZ,CAAC,GAAGY,QAAQ,CAACb,CAAC,KAAK,CAAC,CAAC;MACvEsB,SAAS,GAAG,IAAI9E,KAAK,CACnB6E,IAAI,EACJpE,IAAI,CAACiE,IAAI,CAACjE,IAAI,CAACmE,GAAG,CAACJ,YAAY,IAAI,CAAC,GAAGK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CACtD,CAAC,CAAA;AAEH,IAAA,CACEC,SAAS,EACTA,SAAS,CAACnB,cAAc,CAAC,CAAC,CAAC,CAAC,EAC5BgB,SAAS,EACTA,SAAS,CAAChB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAA;AAE5B;AACA;KACCoB,GAAG,CAAE7C,MAAM,IACV,IAAI,CAACiB,SAAS,CACZD,aAAa,GAAGhB,MAAM,CAAC0B,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,GAAG3B,MAC9D,CACF,CAAC,CACAQ,OAAO,CAAER,MAAM,IAAK;MACnB,IAAI6B,gBAAgB,CAAC7B,MAAM,EAAEoB,WAAW,EAAEC,SAAS,CAAC,EAAE;AACpDf,QAAAA,WAAW,CAACG,IAAI,CAAC,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACtC,CAAC,CAAC,CAACuC,GAAG,CAAClB,MAAM,CAAC,CAAC,CAAA;AACtD,OAAA;AACF,KAAC,CAAC,CAAA;AAEJ,IAAA,OAAOM,WAAW,CAAA;AACpB,GAAA;AAEAwC,EAAAA,YAAYA,GAAG;IACb,MAAMxC,WAAoB,GAAG,EAAE,CAAA;AAC/B;AACJ;AACI;IACAA,WAAW,CAACG,IAAI,CAAC,GAAG,IAAI,CAACJ,YAAY,EAAE,CAAC,CAAA;AACxC;AACA;IACA,MAAM0C,cAAc,GAAG,IAAI,CAAC5D,KAAK,GAAGoB,SAAS,KAAK,CAAC;AACjD;AACA;MACAyC,SAAS,GAAG,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAACtC,CAAC,CAAC;AAClCsE,MAAAA,KAAK,GAAG3C,WAAW,CAACyC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAACF,SAAS,CAAC;AAC/DG,MAAAA,KAAK,GAAG7C,WAAW,CAACyC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAACG,QAAQ,CAACF,SAAS,CAAC;AAC/D;AACAI,MAAAA,gBAAgB,GAAGL,cAAc,GAC7B,IAAI,CAAC9B,SAAS,CAAC,IAAI,CAACjC,EAAE,CAACyC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1C,IAAI,CAACR,SAAS,CACZ,IAAI,CAAC7B,QAAQ,CAACsC,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAACF,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;AACL;MACA4B,YAAY,GAAGC,YAAY,CAACL,KAAK,EAAEG,gBAAgB,CAAC,GAAG,CAAC;AACxDhC,MAAAA,WAAW,GAAGiC,YAAY,GAAGJ,KAAK,GAAGE,KAAK;AAC1C9B,MAAAA,SAAS,GAAGgC,YAAY,GAAGF,KAAK,GAAGF,KAAK,CAAA;AAC1C,IAAA,IAAI,CAAC,IAAI,CAACM,QAAQ,EAAE,EAAE;AACpBjD,MAAAA,WAAW,CAACG,IAAI,CAAC,GAAG,IAAI,CAACU,kBAAkB,CAACC,WAAW,EAAEC,SAAS,CAAC,CAAC,CAAA;AACtE,KAAC,MAAM;AACLf,MAAAA,WAAW,CAACG,IAAI,CAAC,GAAG,IAAI,CAACqB,oBAAoB,CAACV,WAAW,EAAEC,SAAS,CAAC,CAAC,CAAA;AACxE,KAAA;AACA,IAAA,OAAOf,WAAW,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACYkD,EAAAA,aAAaA,GAAG;AACxB,IAAA,QAAQ,IAAI,CAAC1E,OAAO,CAAC2E,cAAc;AACjC,MAAA,KAAK,OAAO;AACV,QAAA,OAAO,IAAI,CAAC9C,YAAY,EAAE,CAAA;AAC5B,MAAA,KAAK,OAAO;AACV,QAAA,OAAO,IAAI,CAACmC,YAAY,EAAE,CAAA;AAC5B,MAAA;AACE,QAAA,OAAO,IAAI,CAACzC,YAAY,EAAE,CAAA;AAC9B,KAAA;AACF,GAAA;AAEOqD,EAAAA,OAAOA,GAAkB;IAC9B,OAAO,IAAI,CAACF,aAAa,EAAE,CAACX,GAAG,CAAEc,KAAK,KAAM;MAC1CC,WAAW,EAAE,IAAI,CAACjF,CAAC;AACnBkF,MAAAA,cAAc,EAAEF,KAAK;MACrBvF,KAAK,EAAE,IAAI,CAACe,KAAK;MACjBC,QAAQ,EAAE,IAAI,CAACA,QAAAA;AACjB,KAAC,CAAC,CAAC,CAAA;AACL,GAAA;AACF;;;;"}