import { configureStore } from '@reduxjs/toolkit';
import projectSlice from './slices/projectSlice';
import uiSlice from './slices/uiSlice';
import historySlice from './slices/historySlice';

export const store = configureStore({
  reducer: {
    project: projectSlice,
    ui: uiSlice,
    history: historySlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['project/addImage', 'project/updateImage'],
        ignoredPaths: ['project.currentProject.images.originalFile'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
