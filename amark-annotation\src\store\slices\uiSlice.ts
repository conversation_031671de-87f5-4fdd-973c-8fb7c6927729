import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  sidebarVisible: boolean;
  propertiesPanelVisible: boolean;
  layersPanelVisible: boolean;
  toolbarVisible: boolean;
  theme: 'light' | 'dark';
  loading: boolean;
  error?: string;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: Date;
  }>;
}

const initialState: UIState = {
  sidebarVisible: true,
  propertiesPanelVisible: true,
  layersPanelVisible: true,
  toolbarVisible: true,
  theme: 'light',
  loading: false,
  notifications: [],
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarVisible = !state.sidebarVisible;
    },

    togglePropertiesPanel: (state) => {
      state.propertiesPanelVisible = !state.propertiesPanelVisible;
    },

    toggleLayersPanel: (state) => {
      state.layersPanelVisible = !state.layersPanelVisible;
    },

    toggleToolbar: (state) => {
      state.toolbarVisible = !state.toolbarVisible;
    },

    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    setError: (state, action: PayloadAction<string | undefined>) => {
      state.error = action.payload;
    },

    addNotification: (state, action: PayloadAction<{
      type: 'success' | 'error' | 'warning' | 'info';
      message: string;
    }>) => {
      const notification = {
        id: crypto.randomUUID(),
        ...action.payload,
        timestamp: new Date(),
      };
      state.notifications.push(notification);
    },

    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },

    clearNotifications: (state) => {
      state.notifications = [];
    },
  },
});

export const {
  toggleSidebar,
  togglePropertiesPanel,
  toggleLayersPanel,
  toggleToolbar,
  setTheme,
  setLoading,
  setError,
  addNotification,
  removeNotification,
  clearNotifications,
} = uiSlice.actions;

export default uiSlice.reducer;
