import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { createProject, addImage } from '../../store/slices/projectSlice';
import { addNotification } from '../../store/slices/uiSlice';

const SimpleMainLayout: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject } = useSelector((state: RootState) => state.project);
  const [projectName, setProjectName] = useState('');

  const handleCreateProject = () => {
    if (projectName.trim()) {
      dispatch(createProject({
        name: projectName.trim(),
        description: '测试项目'
      }));
      dispatch(addNotification({
        type: 'success',
        message: `项目 "${projectName}" 创建成功！`
      }));
      setProjectName('');
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = () => {
        const img = new Image();
        img.onload = () => {
          dispatch(addImage({
            file,
            url: reader.result as string,
            size: { width: img.width, height: img.height }
          }));
          dispatch(addNotification({
            type: 'success',
            message: `图像 "${file.name}" 上传成功！`
          }));
        };
        img.src = reader.result as string;
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div style={{ 
      height: '100vh', 
      display: 'flex', 
      flexDirection: 'column',
      fontFamily: 'Arial, sans-serif'
    }}>
      {/* Header */}
      <div style={{
        height: '60px',
        backgroundColor: '#2196f3',
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        padding: '0 20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px' }}>AMARK</h1>
        <span style={{ marginLeft: '20px', opacity: 0.9 }}>图像标注软件</span>
      </div>

      {/* Main Content */}
      <div style={{ flex: 1, display: 'flex' }}>
        {/* Sidebar */}
        <div style={{
          width: '300px',
          backgroundColor: '#f5f5f5',
          borderRight: '1px solid #ddd',
          padding: '20px'
        }}>
          <h3>项目管理</h3>
          
          {!currentProject ? (
            <div>
              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px' }}>项目名称:</label>
                <input
                  type="text"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  placeholder="输入项目名称"
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                />
              </div>
              <button
                onClick={handleCreateProject}
                disabled={!projectName.trim()}
                style={{
                  width: '100%',
                  padding: '10px',
                  backgroundColor: '#2196f3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: projectName.trim() ? 'pointer' : 'not-allowed',
                  opacity: projectName.trim() ? 1 : 0.5
                }}
              >
                创建项目
              </button>
            </div>
          ) : (
            <div>
              <p><strong>当前项目:</strong> {currentProject.name}</p>
              <p><strong>图像数量:</strong> {currentProject.images.length}</p>
              
              <div style={{ marginTop: '20px' }}>
                <label style={{ display: 'block', marginBottom: '8px' }}>上传图像:</label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  style={{
                    width: '100%',
                    padding: '8px',
                    border: '1px solid #ddd',
                    borderRadius: '4px'
                  }}
                />
              </div>

              {currentProject.images.length > 0 && (
                <div style={{ marginTop: '20px' }}>
                  <h4>图像列表:</h4>
                  {currentProject.images.map((image, index) => (
                    <div key={image.id} style={{
                      padding: '8px',
                      backgroundColor: 'white',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      marginBottom: '8px'
                    }}>
                      <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                        {image.name}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {image.size.width} × {image.size.height}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Main Area */}
        <div style={{ 
          flex: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          backgroundColor: '#fafafa'
        }}>
          {currentProject && currentProject.images.length > 0 ? (
            <div style={{ textAlign: 'center' }}>
              <h2>图像预览</h2>
              <div style={{
                maxWidth: '600px',
                maxHeight: '400px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                overflow: 'hidden',
                backgroundColor: 'white'
              }}>
                <img
                  src={currentProject.images[0].url}
                  alt={currentProject.images[0].name}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }}
                />
              </div>
              <p style={{ marginTop: '10px', color: '#666' }}>
                {currentProject.images[0].name}
              </p>
            </div>
          ) : (
            <div style={{ textAlign: 'center', color: '#666' }}>
              <h2>欢迎使用 AMARK</h2>
              <p>请先创建项目并上传图像开始标注</p>
              
              <div style={{
                marginTop: '40px',
                padding: '20px',
                backgroundColor: 'white',
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                maxWidth: '500px'
              }}>
                <h3>功能特性</h3>
                <ul style={{ textAlign: 'left', lineHeight: '1.8' }}>
                  <li>支持多种标注类型：点、线、矩形、圆形、多边形、自由绘制</li>
                  <li>样式定制：颜色、线宽、线型、填充</li>
                  <li>数据导出：Excel、CSV、JSON格式</li>
                  <li>项目管理和图层组织</li>
                  <li>明暗主题切换</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleMainLayout;
