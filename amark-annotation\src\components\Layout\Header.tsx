import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { createProject, addImage } from '../../store/slices/projectSlice';
import { addNotification } from '../../store/slices/uiSlice';
import {
  toggleSidebar,
  togglePropertiesPanel,
  toggleLayersPanel,
  toggleToolbar,
  setTheme
} from '../../store/slices/uiSlice';
import {
  Menu,
  Folder,
  Save,
  Upload,
  Download,
  Settings,
  Sun,
  Moon,
  PanelLeft,
  PanelRight,
  Layers,
  Wrench,
  Play
} from 'lucide-react';
import ExportDialog from '../Dialogs/ExportDialog';
import { createDemoProject, createDemoImageData } from '../../utils/demoData';
import './Header.css';

const Header: React.FC = () => {
  const dispatch = useDispatch();
  const { currentProject } = useSelector((state: RootState) => state.project);
  const { theme } = useSelector((state: RootState) => state.ui);
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');

  const handleNewProject = () => {
    if (projectName.trim()) {
      dispatch(createProject({
        name: projectName.trim(),
        description: projectDescription.trim() || undefined
      }));
      setShowNewProjectDialog(false);
      setProjectName('');
      setProjectDescription('');
    }
  };

  const handleThemeToggle = () => {
    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));
  };

  const handleLoadDemo = () => {
    // 创建演示项目
    const demoProject = createDemoProject();
    dispatch(createProject({
      name: demoProject.name,
      description: demoProject.description
    }));

    // 添加演示图像数据
    const demoImageData = createDemoImageData();
    dispatch(addImage({
      file: demoImageData.originalFile,
      url: demoImageData.url,
      size: demoImageData.size
    }));

    dispatch(addNotification({
      type: 'success',
      message: '演示数据已加载，您可以开始体验AMARK的功能！'
    }));
  };

  return (
    <header className="header">
      <div className="header-left">
        <div className="logo">
          <h1>AMARK</h1>
        </div>
        
        <nav className="main-menu">
          <div className="menu-item">
            <button className="menu-button">
              <Folder size={16} />
              项目
            </button>
            <div className="dropdown-menu">
              <button onClick={() => setShowNewProjectDialog(true)}>
                新建项目
              </button>
              <button>打开项目</button>
              <button onClick={handleLoadDemo}>
                <Play size={14} />
                加载演示数据
              </button>
              <hr />
              <button disabled={!currentProject}>保存项目</button>
              <button disabled={!currentProject}>另存为</button>
            </div>
          </div>
          
          <div className="menu-item">
            <button className="menu-button">
              <Upload size={16} />
              导入
            </button>
            <div className="dropdown-menu">
              <button>导入图像</button>
              <button>导入标注数据</button>
            </div>
          </div>
          
          <div className="menu-item">
            <button className="menu-button" disabled={!currentProject}>
              <Download size={16} />
              导出
            </button>
            <div className="dropdown-menu">
              <button onClick={() => setShowExportDialog(true)}>导出标注数据</button>
              <button>导出图像</button>
            </div>
          </div>
        </nav>
      </div>
      
      <div className="header-center">
        {currentProject && (
          <div className="project-info">
            <span className="project-name">{currentProject.name}</span>
            <span className="project-status">已保存</span>
          </div>
        )}
      </div>
      
      <div className="header-right">
        <div className="view-controls">
          <button 
            className="icon-button"
            onClick={() => dispatch(toggleSidebar())}
            title="切换侧边栏"
          >
            <PanelLeft size={16} />
          </button>
          
          <button 
            className="icon-button"
            onClick={() => dispatch(toggleToolbar())}
            title="切换工具栏"
          >
            <Wrench size={16} />
          </button>
          
          <button 
            className="icon-button"
            onClick={() => dispatch(toggleLayersPanel())}
            title="切换图层面板"
          >
            <Layers size={16} />
          </button>
          
          <button 
            className="icon-button"
            onClick={() => dispatch(togglePropertiesPanel())}
            title="切换属性面板"
          >
            <PanelRight size={16} />
          </button>
        </div>
        
        <button 
          className="icon-button"
          onClick={handleThemeToggle}
          title="切换主题"
        >
          {theme === 'light' ? <Moon size={16} /> : <Sun size={16} />}
        </button>
        
        <button className="icon-button" title="设置">
          <Settings size={16} />
        </button>
      </div>
      
      {/* 新建项目对话框 */}
      {showNewProjectDialog && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>新建项目</h3>
            <div className="form-group">
              <label>项目名称</label>
              <input
                type="text"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="输入项目名称"
                autoFocus
              />
            </div>
            <div className="form-group">
              <label>项目描述（可选）</label>
              <textarea
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                placeholder="输入项目描述"
                rows={3}
              />
            </div>
            <div className="modal-actions">
              <button 
                className="button secondary"
                onClick={() => setShowNewProjectDialog(false)}
              >
                取消
              </button>
              <button 
                className="button primary"
                onClick={handleNewProject}
                disabled={!projectName.trim()}
              >
                创建
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 导出对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
      />
    </header>
  );
};

export default Header;
