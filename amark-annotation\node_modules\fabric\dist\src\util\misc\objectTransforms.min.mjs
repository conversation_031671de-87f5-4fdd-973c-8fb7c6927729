import{objectWithoutProperties as e}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{Point as t}from"../../Point.min.mjs";import{CENTER as s}from"../../constants.min.mjs";import{makeBoundingBoxFromPoints as l}from"./boundingBoxFromPoints.min.mjs";import{invertTransform as a,multiplyTransformMatrices as i,qrDecompose as n}from"./matrix.min.mjs";const o=["translateX","translateY","scaleX","scaleY"],r=(e,t)=>{const s=a(t),l=i(s,e.calcOwnMatrix());c(e,l)},m=(e,t)=>c(e,i(t,e.calcOwnMatrix())),c=(l,a)=>{const i=n(a),{translateX:r,translateY:m,scaleX:c,scaleY:p}=i,f=e(i,o),w=new t(r,m);l.flipX=!1,l.flipY=!1,Object.assign(l,f),l.set({scaleX:c,scaleY:p}),l.setPositionByOrigin(w,s,s)},p=e=>{e.scaleX=1,e.scaleY=1,e.skewX=0,e.skewY=0,e.flipX=!1,e.flipY=!1,e.rotate(0)},f=e=>({scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,angle:e.angle,left:e.left,flipX:e.flipX,flipY:e.flipY,top:e.top}),w=(e,s,a)=>{const i=e/2,n=s/2,o=[new t(-i,-n),new t(i,-n),new t(-i,n),new t(i,n)].map((e=>e.transform(a))),r=l(o);return new t(r.width,r.height)};export{m as addTransformToObject,c as applyTransformToObject,r as removeTransformFromObject,p as resetObjectTransform,f as saveObjectTransform,w as sizeAfterTransform};
//# sourceMappingURL=objectTransforms.min.mjs.map
