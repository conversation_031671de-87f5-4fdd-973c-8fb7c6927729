{"version": 3, "file": "svgExport.min.mjs", "sources": ["../../../../src/util/misc/svgExport.ts"], "sourcesContent": ["import type { TMat2D } from '../../typedefs';\nimport { toFixed } from './toFixed';\nimport { config } from '../../config';\n\n/**\n * given an array of 6 number returns something like `\"matrix(...numbers)\"`\n * @param {TMat2D} transform an array with 6 numbers\n * @return {String} transform matrix for svg\n */\nexport const matrixToSVG = (transform: TMat2D) =>\n  'matrix(' +\n  transform\n    .map((value) => toFixed(value, config.NUM_FRACTION_DIGITS))\n    .join(' ') +\n  ')';\n"], "names": ["matrixToSVG", "transform", "map", "value", "toFixed", "config", "NUM_FRACTION_DIGITS", "join"], "mappings": "0FASaA,MAAAA,EAAeC,GAC1B,UACAA,EACGC,KAAKC,GAAUC,EAAQD,EAAOE,EAAOC,uBACrCC,KAAK,KACR"}