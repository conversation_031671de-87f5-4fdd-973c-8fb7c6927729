import { Project, ImageData, Marker, MarkerGroup } from '../types';

// 创建演示项目数据
export const createDemoProject = (): Project => {
  const now = new Date();
  
  const demoProject: Project = {
    id: 'demo-project-1',
    name: '演示项目',
    description: '这是一个演示项目，展示了AMARK图像标注软件的各种功能',
    images: [],
    groups: [
      {
        id: 'group-1',
        name: '重要标注',
        color: '#ff0000',
        visible: true,
        childIds: [],
        markerIds: [],
        createdAt: now,
        updatedAt: now,
      },
      {
        id: 'group-2',
        name: '次要标注',
        color: '#00ff00',
        visible: true,
        childIds: [],
        markerIds: [],
        createdAt: now,
        updatedAt: now,
      },
    ],
    settings: {
      defaultMarkerStyle: {
        color: '#ff0000',
        lineWidth: 2,
        lineStyle: 'solid',
        fillColor: '#ff000020',
        fillOpacity: 0.2,
        fontSize: 12,
        fontFamily: 'Arial',
        fontWeight: 'normal',
      },
      autoSave: true,
      autoSaveInterval: 120,
      maxUndoSteps: 50,
      gridEnabled: false,
      gridSize: 10,
      snapToGrid: false,
      measurementUnit: 'px',
      measurementScale: 1,
    },
    createdAt: now,
    updatedAt: now,
  };

  return demoProject;
};

// 创建演示标注数据
export const createDemoMarkers = (): Marker[] => {
  const now = new Date();
  
  return [
    {
      id: 'marker-1',
      name: '点标记示例',
      type: 'point',
      points: [{ x: 100, y: 100 }],
      style: {
        color: '#ff0000',
        lineWidth: 3,
        lineStyle: 'solid',
        fillColor: '#ff0000',
        fillOpacity: 1,
        fontSize: 12,
        fontFamily: 'Arial',
        fontWeight: 'normal',
      },
      groupIds: ['group-1'],
      tags: ['重要', '检查点'],
      visible: true,
      locked: false,
      metadata: {
        description: '这是一个重要的检查点',
        priority: 'high',
      },
      createdAt: now,
      updatedAt: now,
    },
    {
      id: 'marker-2',
      name: '矩形区域',
      type: 'rectangle',
      points: [
        { x: 200, y: 150 },
        { x: 350, y: 250 }
      ],
      style: {
        color: '#00ff00',
        lineWidth: 2,
        lineStyle: 'solid',
        fillColor: '#00ff0030',
        fillOpacity: 0.3,
        fontSize: 12,
        fontFamily: 'Arial',
        fontWeight: 'normal',
      },
      groupIds: ['group-2'],
      tags: ['区域', '分析'],
      visible: true,
      locked: false,
      metadata: {
        area: '感兴趣区域',
        type: 'ROI',
      },
      createdAt: now,
      updatedAt: now,
    },
    {
      id: 'marker-3',
      name: '测量线段',
      type: 'line',
      points: [
        { x: 50, y: 300 },
        { x: 200, y: 300 }
      ],
      style: {
        color: '#0000ff',
        lineWidth: 2,
        lineStyle: 'dashed',
        fontSize: 12,
        fontFamily: 'Arial',
        fontWeight: 'normal',
      },
      groupIds: [],
      tags: ['测量', '距离'],
      visible: true,
      locked: false,
      metadata: {
        length: '150px',
        unit: 'pixel',
      },
      createdAt: now,
      updatedAt: now,
    },
    {
      id: 'marker-4',
      name: '圆形标记',
      type: 'circle',
      points: [
        { x: 400, y: 100 },
        { x: 450, y: 150 }
      ],
      style: {
        color: '#ff00ff',
        lineWidth: 2,
        lineStyle: 'solid',
        fillColor: '#ff00ff20',
        fillOpacity: 0.2,
        fontSize: 12,
        fontFamily: 'Arial',
        fontWeight: 'normal',
      },
      groupIds: ['group-1'],
      tags: ['圆形', '标记'],
      visible: true,
      locked: false,
      metadata: {
        radius: '35px',
        center: '(425, 125)',
      },
      createdAt: now,
      updatedAt: now,
    },
  ];
};

// 生成演示用的Canvas图像数据URL
export const generateDemoImageDataURL = (width: number = 600, height: number = 400): string => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return '';
  
  // 绘制渐变背景
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, '#e3f2fd');
  gradient.addColorStop(1, '#bbdefb');
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);
  
  // 绘制网格
  ctx.strokeStyle = '#90caf9';
  ctx.lineWidth = 1;
  ctx.setLineDash([5, 5]);
  
  for (let x = 0; x <= width; x += 50) {
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
  }
  
  for (let y = 0; y <= height; y += 50) {
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(width, y);
    ctx.stroke();
  }
  
  // 绘制一些装饰性元素
  ctx.setLineDash([]);
  ctx.fillStyle = '#2196f3';
  ctx.fillRect(50, 50, 100, 80);
  
  ctx.fillStyle = '#4caf50';
  ctx.beginPath();
  ctx.arc(450, 100, 40, 0, Math.PI * 2);
  ctx.fill();
  
  ctx.fillStyle = '#ff9800';
  ctx.beginPath();
  ctx.moveTo(300, 200);
  ctx.lineTo(350, 150);
  ctx.lineTo(400, 200);
  ctx.lineTo(375, 250);
  ctx.lineTo(325, 250);
  ctx.closePath();
  ctx.fill();
  
  // 添加文字
  ctx.fillStyle = '#333333';
  ctx.font = '20px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('AMARK 演示图像', width / 2, height - 30);
  
  return canvas.toDataURL('image/png');
};

// 创建演示图像数据
export const createDemoImageData = (): ImageData => {
  const now = new Date();
  const imageDataURL = generateDemoImageDataURL();
  
  // 创建一个虚拟的File对象
  const demoFile = new File([''], 'demo-image.png', { type: 'image/png' });
  
  return {
    id: 'demo-image-1',
    name: 'demo-image.png',
    url: imageDataURL,
    originalFile: demoFile,
    size: { width: 600, height: 400 },
    scale: 1,
    rotation: 0,
    position: { x: 0, y: 0 },
    markers: createDemoMarkers(),
    createdAt: now,
    updatedAt: now,
  };
};
