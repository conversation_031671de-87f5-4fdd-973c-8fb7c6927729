{"version": 3, "file": "resolveOrigin.mjs", "sources": ["../../../../src/util/misc/resolveOrigin.ts"], "sourcesContent": ["import type { TOriginX, TOriginY } from '../../typedefs';\n\nconst originOffset = {\n  left: -0.5,\n  top: -0.5,\n  center: 0,\n  bottom: 0.5,\n  right: 0.5,\n};\n/**\n * Resolves origin value relative to center\n * @private\n * @param {TOriginX | TOriginY} originValue originX / originY\n * @returns number\n */\n\nexport const resolveOrigin = (\n  originValue: TOriginX | TOriginY | number,\n): number =>\n  typeof originValue === 'string'\n    ? originOffset[originValue]\n    : originValue - 0.5;\n"], "names": ["originOffset", "left", "top", "center", "bottom", "right", "<PERSON><PERSON><PERSON><PERSON>", "originValue"], "mappings": "AAEA,MAAMA,YAAY,GAAG;EACnBC,IAAI,EAAE,CAAC,GAAG;EACVC,GAAG,EAAE,CAAC,GAAG;AACTC,EAAAA,MAAM,EAAE,CAAC;AACTC,EAAAA,MAAM,EAAE,GAAG;AACXC,EAAAA,KAAK,EAAE,GAAA;AACT,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;;MAEaC,aAAa,GACxBC,WAAyC,IAEzC,OAAOA,WAAW,KAAK,QAAQ,GAC3BP,YAAY,CAACO,WAAW,CAAC,GACzBA,WAAW,GAAG;;;;"}