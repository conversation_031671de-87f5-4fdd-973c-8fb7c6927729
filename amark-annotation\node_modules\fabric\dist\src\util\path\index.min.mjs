import{objectSpread2 as t}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{cache as e}from"../../cache.min.mjs";import{config as n}from"../../config.min.mjs";import{halfPI as s,PiBy180 as r}from"../../constants.min.mjs";import{cos as a}from"../misc/cos.min.mjs";import{multiplyTransformMatrices as o,transformPoint as c}from"../misc/matrix.min.mjs";import{sin as l}from"../misc/sin.min.mjs";import{toFixed as i}from"../misc/toFixed.min.mjs";import{Point as h}from"../../Point.min.mjs";import{rePathCommand as m,reArcCommandPoints as u}from"./regex.min.mjs";import{reNum as f}from"../../parser/constants.min.mjs";const x={m:"l",M:"L"},p=(t,e,n,s,r,o,c,i,h,m,u)=>{const f=a(t),x=l(t),p=a(e),g=l(e),y=n*r*p-s*o*g+c,M=s*r*p+n*o*g+i;return["C",m+h*(-n*r*x-s*o*f),u+h*(-s*r*x+n*o*f),y+h*(n*r*g+s*o*p),M+h*(s*r*g-n*o*p),y,M]},g=(t,e,n,s)=>{const r=Math.atan2(e,t),a=Math.atan2(s,n);return a>=r?a-r:2*Math.PI-(r-a)};function y(t,s,r,a,o,c,l,i){let m;if(n.cachesBoundsOfCurve&&(m=[...arguments].join(),e.boundsOfCurveCache[m]))return e.boundsOfCurveCache[m];const u=Math.sqrt,f=Math.abs,x=[],p=[[0,0],[0,0]];let g=6*t-12*r+6*o,y=-3*t+9*r-9*o+3*l,M=3*r-3*t;for(let t=0;t<2;++t){if(t>0&&(g=6*s-12*a+6*c,y=-3*s+9*a-9*c+3*i,M=3*a-3*s),f(y)<1e-12){if(f(g)<1e-12)continue;const t=-M/g;0<t&&t<1&&x.push(t);continue}const e=g*g-4*M*y;if(e<0)continue;const n=u(e),r=(-g+n)/(2*y);0<r&&r<1&&x.push(r);const o=(-g-n)/(2*y);0<o&&o<1&&x.push(o)}let d=x.length;const b=d,C=w(t,s,r,a,o,c,l,i);for(;d--;){const{x:t,y:e}=C(x[d]);p[0][d]=t,p[1][d]=e}p[0][b]=t,p[1][b]=s,p[0][b+1]=l,p[1][b+1]=i;const j=[new h(Math.min(...p[0]),Math.min(...p[1])),new h(Math.max(...p[0]),Math.max(...p[1]))];return n.cachesBoundsOfCurve&&(e.boundsOfCurveCache[m]=j),j}const M=(t,e,n)=>{let[s,o,c,i,h,m,u,f]=n;const x=((t,e,n,s,o,c,i)=>{if(0===n||0===s)return[];let h=0,m=0,u=0;const f=Math.PI,x=i*r,y=l(x),M=a(x),d=.5*(-M*t-y*e),b=.5*(-M*e+y*t),w=n**2,C=s**2,j=b**2,k=d**2,v=w*C-w*j-C*k;let L=Math.abs(n),q=Math.abs(s);if(v<0){const t=Math.sqrt(1-v/(w*C));L*=t,q*=t}else u=(o===c?-1:1)*Math.sqrt(v/(w*j+C*k));const Q=u*L*b/q,F=-u*q*d/L,P=M*Q-y*F+.5*t,Z=y*Q+M*F+.5*e;let O=g(1,0,(d-Q)/L,(b-F)/q),A=g((d-Q)/L,(b-F)/q,(-d-Q)/L,(-b-F)/q);0===c&&A>0?A-=2*f:1===c&&A<0&&(A+=2*f);const E=Math.ceil(Math.abs(A/f*2)),I=[],B=A/E,R=8/3*Math.sin(B/4)*Math.sin(B/4)/Math.sin(B/2);let X=O+B;for(let t=0;t<E;t++)I[t]=p(O,X,M,y,L,q,P,Z,R,h,m),h=I[t][5],m=I[t][6],O=X,X+=B;return I})(u-t,f-e,o,c,h,m,i);for(let n=0,s=x.length;n<s;n++)x[n][1]+=t,x[n][2]+=e,x[n][3]+=t,x[n][4]+=e,x[n][5]+=t,x[n][6]+=e;return x},d=t=>{let e=0,n=0,s=0,r=0;const a=[];let o,c=0,l=0;for(const i of t){const t=[...i];let h;switch(t[0]){case"l":t[1]+=e,t[2]+=n;case"L":e=t[1],n=t[2],h=["L",e,n];break;case"h":t[1]+=e;case"H":e=t[1],h=["L",e,n];break;case"v":t[1]+=n;case"V":n=t[1],h=["L",e,n];break;case"m":t[1]+=e,t[2]+=n;case"M":e=t[1],n=t[2],s=t[1],r=t[2],h=["M",e,n];break;case"c":t[1]+=e,t[2]+=n,t[3]+=e,t[4]+=n,t[5]+=e,t[6]+=n;case"C":c=t[3],l=t[4],e=t[5],n=t[6],h=["C",t[1],t[2],c,l,e,n];break;case"s":t[1]+=e,t[2]+=n,t[3]+=e,t[4]+=n;case"S":"C"===o?(c=2*e-c,l=2*n-l):(c=e,l=n),e=t[3],n=t[4],h=["C",c,l,t[1],t[2],e,n],c=h[3],l=h[4];break;case"q":t[1]+=e,t[2]+=n,t[3]+=e,t[4]+=n;case"Q":c=t[1],l=t[2],e=t[3],n=t[4],h=["Q",c,l,e,n];break;case"t":t[1]+=e,t[2]+=n;case"T":"Q"===o?(c=2*e-c,l=2*n-l):(c=e,l=n),e=t[1],n=t[2],h=["Q",c,l,e,n];break;case"a":t[6]+=e,t[7]+=n;case"A":M(e,n,t).forEach((t=>a.push(t))),e=t[6],n=t[7];break;case"z":case"Z":e=s,n=r,h=["Z"]}h?(a.push(h),o=h[0]):o=""}return a},b=(t,e,n,s)=>Math.sqrt((n-t)**2+(s-e)**2),w=(t,e,n,s,r,a,o,c)=>l=>{const i=l**3,m=(t=>3*t**2*(1-t))(l),u=(t=>3*t*(1-t)**2)(l),f=(t=>(1-t)**3)(l);return new h(o*i+r*m+n*u+t*f,c*i+a*m+s*u+e*f)},C=t=>t**2,j=t=>2*t*(1-t),k=t=>(1-t)**2,v=(t,e,n,s,r,a,o,c)=>l=>{const i=C(l),h=j(l),m=k(l),u=3*(m*(n-t)+h*(r-n)+i*(o-r)),f=3*(m*(s-e)+h*(a-s)+i*(c-a));return Math.atan2(f,u)},L=(t,e,n,s,r,a)=>o=>{const c=C(o),l=j(o),i=k(o);return new h(r*c+n*l+t*i,a*c+s*l+e*i)},q=(t,e,n,s,r,a)=>o=>{const c=1-o,l=2*(c*(n-t)+o*(r-n)),i=2*(c*(s-e)+o*(a-s));return Math.atan2(i,l)},Q=(t,e,n)=>{let s=new h(e,n),r=0;for(let e=1;e<=100;e+=1){const n=t(e/100);r+=b(s.x,s.y,n.x,n.y),s=n}return r},F=(e,n)=>{let s,r=0,a=0,o={x:e.x,y:e.y},c=t({},o),l=.01,i=0;const h=e.iterator,m=e.angleFinder;for(;a<n&&l>1e-4;)c=h(r),i=r,s=b(o.x,o.y,c.x,c.y),s+a>n?(r-=l,l/=2):(o=c,r+=l,a+=s);return t(t({},c),{},{angle:m(i)})},P=t=>{let e,n,s=0,r=0,a=0,o=0,c=0;const l=[];for(const i of t){const t={x:r,y:a,command:i[0],length:0};switch(i[0]){case"M":n=t,n.x=o=r=i[1],n.y=c=a=i[2];break;case"L":n=t,n.length=b(r,a,i[1],i[2]),r=i[1],a=i[2];break;case"C":e=w(r,a,i[1],i[2],i[3],i[4],i[5],i[6]),n=t,n.iterator=e,n.angleFinder=v(r,a,i[1],i[2],i[3],i[4],i[5],i[6]),n.length=Q(e,r,a),r=i[5],a=i[6];break;case"Q":e=L(r,a,i[1],i[2],i[3],i[4]),n=t,n.iterator=e,n.angleFinder=q(r,a,i[1],i[2],i[3],i[4]),n.length=Q(e,r,a),r=i[3],a=i[4];break;case"Z":n=t,n.destX=o,n.destY=c,n.length=b(r,a,o,c),r=o,a=c}s+=n.length,l.push(n)}return l.push({length:s,x:r,y:a}),l},Z=function(e,n){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:P(e),r=0;for(;n-s[r].length>0&&r<s.length-2;)n-=s[r].length,r++;const a=s[r],o=n/a.length,c=e[r];switch(a.command){case"M":return{x:a.x,y:a.y,angle:0};case"Z":return t(t({},new h(a.x,a.y).lerp(new h(a.destX,a.destY),o)),{},{angle:Math.atan2(a.destY-a.y,a.destX-a.x)});case"L":return t(t({},new h(a.x,a.y).lerp(new h(c[1],c[2]),o)),{},{angle:Math.atan2(c[2]-a.y,c[1]-a.x)});case"C":case"Q":return F(a,n)}},O=new RegExp(m,"gi"),A=new RegExp(u,"g"),E=new RegExp(f,"gi"),I={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},B=t=>{var e;const n=[],s=null!==(e=t.match(O))&&void 0!==e?e:[];for(const t of s){const e=t[0];if("z"===e||"Z"===e){n.push([e]);continue}const s=I[e.toLowerCase()];let r=[];if("a"===e||"A"===e){A.lastIndex=0;for(let e=null;e=A.exec(t);)r.push(...e.slice(1))}else r=t.match(E)||[];for(let t=0;t<r.length;t+=s){const a=new Array(s),o=x[e];a[0]=t>0&&o?o:e;for(let e=0;e<s;e++)a[e+1]=parseFloat(r[t+e]);n.push(a)}}return n},R=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=new h(t[0]),s=new h(t[1]),r=1,a=0;const o=[],c=t.length,l=c>2;let i;for(l&&(r=t[2].x<s.x?-1:t[2].x===s.x?0:1,a=t[2].y<s.y?-1:t[2].y===s.y?0:1),o.push(["M",n.x-r*e,n.y-a*e]),i=1;i<c;i++){if(!n.eq(s)){const t=n.midPointFrom(s);o.push(["Q",n.x,n.y,t.x,t.y])}n=t[i],i+1<t.length&&(s=t[i+1])}return l&&(r=n.x>t[i-2].x?1:n.x===t[i-2].x?0:-1,a=n.y>t[i-2].y?1:n.y===t[i-2].y?0:-1),o.push(["L",n.x+r*e,n.y+a*e]),o},X=(t,e,n)=>(n&&(e=o(e,[1,0,0,1,-n.x,-n.y])),t.map((t=>{const n=[...t];for(let s=1;s<t.length-1;s+=2){const{x:r,y:a}=c({x:t[s],y:t[s+1]},e);n[s]=r,n[s+1]=a}return n}))),Y=(t,e)=>{const n=2*Math.PI/t;let r=-s;t%2==0&&(r+=n/2);const o=new Array(t+1);for(let s=0;s<t;s++){const t=s*n+r,{x:c,y:i}=new h(a(t),l(t)).scalarMultiply(e);o[s]=[0===s?"M":"L",c,i]}return o[t]=["Z"],o},z=(t,e)=>t.map((t=>t.map(((t,n)=>0===n||void 0===e?t:i(t,e))).join(" "))).join(" ");export{M as fromArcToBeziers,y as getBoundsOfCurve,P as getPathSegmentsInfo,Z as getPointOnPath,Y as getRegularPolygonPath,R as getSmoothPathFromPoints,z as joinPath,d as makePathSimpler,B as parsePath,X as transformPath};
//# sourceMappingURL=index.min.mjs.map
