import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { ExportOptions } from '../../types';
import { exportToExcel, exportToCSV, exportToJSON, generateExportPreview } from '../../utils/exportUtils';
import { Download, FileText, Table, Code } from 'lucide-react';
import './ExportDialog.css';

interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const ExportDialog: React.FC<ExportDialogProps> = ({ isOpen, onClose }) => {
  const { currentProject } = useSelector((state: RootState) => state.project);
  
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'xlsx',
    includeMetadata: true,
    includeTimestamps: true,
    includeStyles: true,
    selectedMarkersOnly: false,
    selectedGroupsOnly: false,
  });

  if (!isOpen || !currentProject) return null;

  const preview = generateExportPreview(currentProject, exportOptions);

  const handleExport = () => {
    switch (exportOptions.format) {
      case 'xlsx':
        exportToExcel(currentProject, exportOptions);
        break;
      case 'csv':
        exportToCSV(currentProject, exportOptions);
        break;
      case 'json':
        exportToJSON(currentProject, exportOptions);
        break;
    }
    onClose();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  return (
    <div className="modal-overlay">
      <div className="export-dialog">
        <div className="dialog-header">
          <h2>导出标注数据</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="dialog-content">
          <div className="export-options">
            <div className="option-group">
              <h3>导出格式</h3>
              <div className="format-options">
                <label className={`format-option ${exportOptions.format === 'xlsx' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    name="format"
                    value="xlsx"
                    checked={exportOptions.format === 'xlsx'}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as 'xlsx' }))}
                  />
                  <div className="format-content">
                    <Table size={24} />
                    <span>Excel (.xlsx)</span>
                    <small>适合数据分析和表格处理</small>
                  </div>
                </label>

                <label className={`format-option ${exportOptions.format === 'csv' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    name="format"
                    value="csv"
                    checked={exportOptions.format === 'csv'}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as 'csv' }))}
                  />
                  <div className="format-content">
                    <FileText size={24} />
                    <span>CSV (.csv)</span>
                    <small>通用格式，兼容性好</small>
                  </div>
                </label>

                <label className={`format-option ${exportOptions.format === 'json' ? 'selected' : ''}`}>
                  <input
                    type="radio"
                    name="format"
                    value="json"
                    checked={exportOptions.format === 'json'}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, format: e.target.value as 'json' }))}
                  />
                  <div className="format-content">
                    <Code size={24} />
                    <span>JSON (.json)</span>
                    <small>适合程序化处理</small>
                  </div>
                </label>
              </div>
            </div>

            <div className="option-group">
              <h3>包含内容</h3>
              <div className="checkbox-options">
                <label className="checkbox-option">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeStyles}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, includeStyles: e.target.checked }))}
                  />
                  <span>样式信息（颜色、线宽等）</span>
                </label>

                <label className="checkbox-option">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeTimestamps}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, includeTimestamps: e.target.checked }))}
                  />
                  <span>时间戳（创建和修改时间）</span>
                </label>

                <label className="checkbox-option">
                  <input
                    type="checkbox"
                    checked={exportOptions.includeMetadata}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, includeMetadata: e.target.checked }))}
                  />
                  <span>元数据（标签、自定义字段）</span>
                </label>
              </div>
            </div>

            <div className="option-group">
              <h3>导出范围</h3>
              <div className="checkbox-options">
                <label className="checkbox-option">
                  <input
                    type="checkbox"
                    checked={exportOptions.selectedMarkersOnly}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, selectedMarkersOnly: e.target.checked }))}
                  />
                  <span>仅导出选中的标注</span>
                </label>

                <label className="checkbox-option">
                  <input
                    type="checkbox"
                    checked={exportOptions.selectedGroupsOnly}
                    onChange={(e) => setExportOptions(prev => ({ ...prev, selectedGroupsOnly: e.target.checked }))}
                  />
                  <span>仅导出选中的分组</span>
                </label>
              </div>
            </div>
          </div>

          <div className="export-preview">
            <h3>导出预览</h3>
            <div className="preview-stats">
              <div className="stat-item">
                <span className="stat-label">图像数量:</span>
                <span className="stat-value">{preview.totalImages}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">标注数量:</span>
                <span className="stat-value">{preview.totalMarkers}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">预估文件大小:</span>
                <span className="stat-value">{formatFileSize(preview.estimatedFileSize)}</span>
              </div>
            </div>

            <div className="marker-types">
              <h4>标注类型分布:</h4>
              <div className="type-list">
                {Object.entries(preview.markerTypes).map(([type, count]) => (
                  <div key={type} className="type-item">
                    <span className="type-name">{type}</span>
                    <span className="type-count">{count}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="dialog-actions">
          <button className="button secondary" onClick={onClose}>
            取消
          </button>
          <button className="button primary" onClick={handleExport}>
            <Download size={16} />
            导出
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportDialog;
