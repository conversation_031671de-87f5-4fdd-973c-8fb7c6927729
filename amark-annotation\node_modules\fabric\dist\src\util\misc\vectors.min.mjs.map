{"version": 3, "file": "vectors.min.mjs", "sources": ["../../../../src/util/misc/vectors.ts"], "sourcesContent": ["import type { XY } from '../../Point';\nimport { Point } from '../../Point';\nimport type { TRadian } from '../../typedefs';\n\nconst unitVectorX = new Point(1, 0);\nconst zero = new Point();\n\n/**\n * Rotates `vector` with `radians`\n * @param {Point} vector The vector to rotate (x and y)\n * @param {Number} radians The radians of the angle for the rotation\n * @return {Point} The new rotated point\n */\nexport const rotateVector = (vector: Point, radians: TRadian) =>\n  vector.rotate(radians);\n\n/**\n * Creates a vector from points represented as a point\n *\n * @param {Point} from\n * @param {Point} to\n * @returns {Point} vector\n */\nexport const createVector = (from: XY, to: XY): Point =>\n  new Point(to).subtract(from);\n\n/**\n * return the magnitude of a vector\n * @return {number}\n */\nexport const magnitude = (point: Point) => point.distanceFrom(zero);\n\n/**\n * Calculates the angle between 2 vectors\n * @param {Point} a\n * @param {Point} b\n * @returns the angle in radians from `a` to `b`\n */\nexport const calcAngleBetweenVectors = (a: Point, b: Point): TRadian =>\n  Math.atan2(crossProduct(a, b), dotProduct(a, b)) as TRadian;\n\n/**\n * Calculates the angle between the x axis and the vector\n * @param {Point} v\n * @returns the angle in radians of `v`\n */\nexport const calcVectorRotation = (v: Point) =>\n  calcAngleBetweenVectors(unitVectorX, v);\n\n/**\n * @param {Point} v\n * @returns {Point} vector representing the unit vector pointing to the direction of `v`\n */\nexport const getUnitVector = (v: Point): Point =>\n  v.eq(zero) ? v : v.scalarDivide(magnitude(v));\n\n/**\n * @param {Point} v\n * @param {Boolean} [counterClockwise] the direction of the orthogonal vector, defaults to `true`\n * @returns {Point} the unit orthogonal vector\n */\nexport const getOrthonormalVector = (\n  v: Point,\n  counterClockwise = true,\n): Point =>\n  getUnitVector(new Point(-v.y, v.x).scalarMultiply(counterClockwise ? 1 : -1));\n\n/**\n * Cross product of two vectors in 2D\n * @param {Point} a\n * @param {Point} b\n * @returns {number} the magnitude of Z vector\n */\nexport const crossProduct = (a: Point, b: Point): number =>\n  a.x * b.y - a.y * b.x;\n\n/**\n * Dot product of two vectors in 2D\n * @param {Point} a\n * @param {Point} b\n * @returns {number}\n */\nexport const dotProduct = (a: Point, b: Point): number => a.x * b.x + a.y * b.y;\n\n/**\n * Checks if the vector is between two others. It is considered\n * to be inside when the vector to be tested is between the\n * initial vector and the final vector (included) in a counterclockwise direction.\n * @param {Point} t vector to be tested\n * @param {Point} a initial vector\n * @param {Point} b final vector\n * @returns {boolean} true if the vector is among the others\n */\nexport const isBetweenVectors = (t: Point, a: Point, b: Point): boolean => {\n  if (t.eq(a) || t.eq(b)) return true;\n  const AxB = crossProduct(a, b),\n    AxT = crossProduct(a, t),\n    BxT = crossProduct(b, t);\n  return AxB >= 0 ? AxT >= 0 && BxT <= 0 : !(AxT <= 0 && BxT >= 0);\n};\n"], "names": ["unitVectorX", "Point", "zero", "rotateVector", "vector", "radians", "rotate", "createVector", "from", "to", "subtract", "magnitude", "point", "distanceFrom", "calcAngleBetweenVectors", "a", "b", "Math", "atan2", "crossProduct", "dotProduct", "calcVectorRotation", "v", "getUnitVector", "eq", "scalarDivide", "getOrthonormalVector", "counterClockwise", "arguments", "length", "undefined", "y", "x", "scalar<PERSON>ultiply", "isBetweenVectors", "t", "AxB", "AxT", "BxT"], "mappings": "4CAIA,MAAMA,EAAc,IAAIC,EAAM,EAAG,GAC3BC,EAAO,IAAID,EAQJE,EAAeA,CAACC,EAAeC,IAC1CD,EAAOE,OAAOD,GASHE,EAAeA,CAACC,EAAUC,IACrC,IAAIR,EAAMQ,GAAIC,SAASF,GAMZG,EAAaC,GAAiBA,EAAMC,aAAaX,GAQjDY,EAA0BA,CAACC,EAAUC,IAChDC,KAAKC,MAAMC,EAAaJ,EAAGC,GAAII,EAAWL,EAAGC,IAOlCK,EAAsBC,GACjCR,EAAwBd,EAAasB,GAM1BC,EAAiBD,GAC5BA,EAAEE,GAAGtB,GAAQoB,EAAIA,EAAEG,aAAad,EAAUW,IAO/BI,EAAuB,SAClCJ,GAAQ,IACRK,IAAgBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,KAAAA,UAAA,GAAO,OAEvBL,EAAc,IAAItB,GAAOqB,EAAES,EAAGT,EAAEU,GAAGC,eAAeN,EAAmB,GAAK,GAAG,EAQlER,EAAeA,CAACJ,EAAUC,IACrCD,EAAEiB,EAAIhB,EAAEe,EAAIhB,EAAEgB,EAAIf,EAAEgB,EAQTZ,EAAaA,CAACL,EAAUC,IAAqBD,EAAEiB,EAAIhB,EAAEgB,EAAIjB,EAAEgB,EAAIf,EAAEe,EAWjEG,EAAmBA,CAACC,EAAUpB,EAAUC,KACnD,GAAImB,EAAEX,GAAGT,IAAMoB,EAAEX,GAAGR,GAAI,OAAO,EAC/B,MAAMoB,EAAMjB,EAAaJ,EAAGC,GAC1BqB,EAAMlB,EAAaJ,EAAGoB,GACtBG,EAAMnB,EAAaH,EAAGmB,GACxB,OAAOC,GAAO,EAAIC,GAAO,GAAKC,GAAO,IAAMD,GAAO,GAAKC,GAAO,EAAE"}