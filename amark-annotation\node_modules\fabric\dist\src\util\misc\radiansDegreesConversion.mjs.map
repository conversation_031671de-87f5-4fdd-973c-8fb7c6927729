{"version": 3, "file": "radiansDegreesConversion.mjs", "sources": ["../../../../src/util/misc/radiansDegreesConversion.ts"], "sourcesContent": ["import type { TRadian, TDegree } from '../../typedefs';\nimport { PiBy180 } from '../../constants';\n\n/**\n * Transforms degrees to radians.\n * @param {TDegree} degrees value in degrees\n * @return {TRadian} value in radians\n */\nexport const degreesToRadians = (degrees: TDegree): TRadian =>\n  (degrees * PiBy180) as TRadian;\n\n/**\n * Transforms radians to degrees.\n * @param {TRadian} radians value in radians\n * @return {TDegree} value in degrees\n */\nexport const radiansToDegrees = (radians: TRadian): TDegree =>\n  (radians / PiBy180) as TDegree;\n"], "names": ["degreesToRadians", "degrees", "PiBy180", "radiansToDegrees", "radians"], "mappings": ";;AAGA;AACA;AACA;AACA;AACA;MACaA,gBAAgB,GAAIC,OAAgB,IAC9CA,OAAO,GAAGC,QAAmB;;AAEhC;AACA;AACA;AACA;AACA;MACaC,gBAAgB,GAAIC,OAAgB,IAC9CA,OAAO,GAAGF;;;;"}