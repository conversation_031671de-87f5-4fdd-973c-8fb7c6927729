{"version": 3, "file": "planeChange.mjs", "sources": ["../../../../src/util/misc/planeChange.ts"], "sourcesContent": ["import { iMatrix } from '../../constants';\nimport type { Point } from '../../Point';\nimport type { FabricObject } from '../../shapes/Object/Object';\nimport type { TMat2D } from '../../typedefs';\nimport { invertTransform, multiplyTransformMatrices } from './matrix';\nimport { applyTransformToObject } from './objectTransforms';\n\n/**\n * We are actually looking for the transformation from the destination plane to the source plane (change of basis matrix)\\\n * The object will exist on the destination plane and we want it to seem unchanged by it so we invert the destination matrix (`to`) and then apply the source matrix (`from`)\n * @param [from]\n * @param [to]\n * @returns\n */\nexport const calcPlaneChangeMatrix = (\n  from: TMat2D = iMatrix,\n  to: TMat2D = iMatrix,\n) => multiplyTransformMatrices(invertTransform(to), from);\n\n/**\n * Sends a point from the source coordinate plane to the destination coordinate plane.\\\n * From the canvas/viewer's perspective the point remains unchanged.\n *\n * @example <caption>Send point from canvas plane to group plane</caption>\n * var obj = new Rect({ left: 20, top: 20, width: 60, height: 60, strokeWidth: 0 });\n * var group = new Group([obj], { strokeWidth: 0 });\n * var sentPoint1 = sendPointToPlane(new Point(50, 50), undefined, group.calcTransformMatrix());\n * var sentPoint2 = sendPointToPlane(new Point(50, 50), iMatrix, group.calcTransformMatrix());\n * console.log(sentPoint1, sentPoint2) //  both points print (0,0) which is the center of group\n *\n * @param {Point} point\n * @param {TMat2D} [from] plane matrix containing object. Passing `undefined` is equivalent to passing the identity matrix, which means `point` exists in the canvas coordinate plane.\n * @param {TMat2D} [to] destination plane matrix to contain object. Passing `undefined` means `point` should be sent to the canvas coordinate plane.\n * @returns {Point} transformed point\n */\nexport const sendPointToPlane = (\n  point: Point,\n  from: TMat2D = iMatrix,\n  to: TMat2D = iMatrix,\n): Point => point.transform(calcPlaneChangeMatrix(from, to));\n\n/**\n * See {@link sendPointToPlane}\n */\nexport const sendVectorToPlane = (\n  point: Point,\n  from: TMat2D = iMatrix,\n  to: TMat2D = iMatrix,\n): Point => point.transform(calcPlaneChangeMatrix(from, to), true);\n\n/**\n *\n * A util that abstracts applying transform to objects.\\\n * Sends `object` to the destination coordinate plane by applying the relevant transformations.\\\n * Changes the space/plane where `object` is drawn.\\\n * From the canvas/viewer's perspective `object` remains unchanged.\n *\n * @example <caption>Move clip path from one object to another while preserving it's appearance as viewed by canvas/viewer</caption>\n * let obj, obj2;\n * let clipPath = new Circle({ radius: 50 });\n * obj.clipPath = clipPath;\n * // render\n * sendObjectToPlane(clipPath, obj.calcTransformMatrix(), obj2.calcTransformMatrix());\n * obj.clipPath = undefined;\n * obj2.clipPath = clipPath;\n * // render, clipPath now clips obj2 but seems unchanged from the eyes of the viewer\n *\n * @example <caption>Clip an object's clip path with an existing object</caption>\n * let obj, existingObj;\n * let clipPath = new Circle({ radius: 50 });\n * obj.clipPath = clipPath;\n * let transformTo = multiplyTransformMatrices(obj.calcTransformMatrix(), clipPath.calcTransformMatrix());\n * sendObjectToPlane(existingObj, existingObj.group?.calcTransformMatrix(), transformTo);\n * clipPath.clipPath = existingObj;\n *\n * @param {FabricObject} object\n * @param {Matrix} [from] plane matrix containing object. Passing `undefined` is equivalent to passing the identity matrix, which means `object` is a direct child of canvas.\n * @param {Matrix} [to] destination plane matrix to contain object. Passing `undefined` means `object` should be sent to the canvas coordinate plane.\n * @returns {Matrix} the transform matrix that was applied to `object`\n */\nexport const sendObjectToPlane = (\n  object: FabricObject,\n  from?: TMat2D,\n  to?: TMat2D,\n): TMat2D => {\n  const t = calcPlaneChangeMatrix(from, to);\n  applyTransformToObject(\n    object,\n    multiplyTransformMatrices(t, object.calcOwnMatrix()),\n  );\n  return t;\n};\n"], "names": ["calcPlaneChangeMatrix", "from", "arguments", "length", "undefined", "iMatrix", "to", "multiplyTransformMatrices", "invertTransform", "sendPointToPlane", "point", "transform", "sendVectorToPlane", "sendObjectToPlane", "object", "t", "applyTransformToObject", "calcOwnMatrix"], "mappings": ";;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACaA,MAAAA,qBAAqB,GAAG,YAAA;AAAA,EAAA,IACnCC,IAAY,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;AAAA,EAAA,IACtBC,EAAU,GAAAJ,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;EAAA,OACjBE,yBAAyB,CAACC,eAAe,CAACF,EAAE,CAAC,EAAEL,IAAI,CAAC,CAAA;AAAA,EAAA;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACaQ,MAAAA,gBAAgB,GAAG,UAC9BC,KAAY,EAAA;AAAA,EAAA,IACZT,IAAY,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;AAAA,EAAA,IACtBC,EAAU,GAAAJ,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;EAAA,OACVK,KAAK,CAACC,SAAS,CAACX,qBAAqB,CAACC,IAAI,EAAEK,EAAE,CAAC,CAAC,CAAA;AAAA,EAAA;;AAE5D;AACA;AACA;AACaM,MAAAA,iBAAiB,GAAG,UAC/BF,KAAY,EAAA;AAAA,EAAA,IACZT,IAAY,GAAAC,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;AAAA,EAAA,IACtBC,EAAU,GAAAJ,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAAE,SAAA,GAAAF,SAAA,CAAA,CAAA,CAAA,GAAGG,OAAO,CAAA;AAAA,EAAA,OACVK,KAAK,CAACC,SAAS,CAACX,qBAAqB,CAACC,IAAI,EAAEK,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;AAAA,EAAA;;AAElE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMO,iBAAiB,GAAGA,CAC/BC,MAAoB,EACpBb,IAAa,EACbK,EAAW,KACA;AACX,EAAA,MAAMS,CAAC,GAAGf,qBAAqB,CAACC,IAAI,EAAEK,EAAE,CAAC,CAAA;AACzCU,EAAAA,sBAAsB,CACpBF,MAAM,EACNP,yBAAyB,CAACQ,CAAC,EAAED,MAAM,CAACG,aAAa,EAAE,CACrD,CAAC,CAAA;AACD,EAAA,OAAOF,CAAC,CAAA;AACV;;;;"}