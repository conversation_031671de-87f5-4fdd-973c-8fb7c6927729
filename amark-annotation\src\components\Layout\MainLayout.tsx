import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import Header from './Header';
import Sidebar from './Sidebar';
import Toolbar from './Toolbar';
import ImageCanvas from '../Canvas/ImageCanvas';
import PropertiesPanel from './PropertiesPanel';
import LayersPanel from './LayersPanel';
import NotificationContainer from './NotificationContainer';
import './MainLayout.css';

const MainLayout: React.FC = () => {
  const {
    sidebarVisible,
    propertiesPanelVisible,
    layersPanelVisible,
    toolbarVisible,
    theme
  } = useSelector((state: RootState) => state.ui);

  const { currentProject } = useSelector((state: RootState) => state.project);

  return (
    <div className={`main-layout ${theme}`}>
      <Header />
      
      <div className="layout-body">
        {sidebarVisible && (
          <div className="sidebar-container">
            <Sidebar />
          </div>
        )}
        
        <div className="main-content">
          {toolbarVisible && (
            <div className="toolbar-container">
              <Toolbar />
            </div>
          )}
          
          <div className="canvas-container">
            {currentProject ? (
              <ImageCanvas />
            ) : (
              <div className="welcome-screen">
                <h2>欢迎使用 AMARK 图像标注软件</h2>
                <p>请创建新项目或打开现有项目开始标注</p>
              </div>
            )}
          </div>
        </div>
        
        <div className="right-panels">
          {layersPanelVisible && (
            <div className="layers-panel-container">
              <LayersPanel />
            </div>
          )}
          
          {propertiesPanelVisible && (
            <div className="properties-panel-container">
              <PropertiesPanel />
            </div>
          )}
        </div>
      </div>

      {/* 通知容器 */}
      <NotificationContainer />
    </div>
  );
};

export default MainLayout;
