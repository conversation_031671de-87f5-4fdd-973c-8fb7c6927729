# AMARK 图像标注软件

一个功能强大的图像标注软件，支持多种标注类型、批量操作和数据导入导出功能。

## 功能特性

### 🖼️ 图像管理
- 支持多种图像格式（JPEG, PNG, BMP, TIFF）
- 拖拽上传图像文件
- 图像缩放、平移和旋转
- 项目管理，支持多图像标注

### ✏️ 标注工具
- **点标记**: 单点标注
- **线段**: 两点连线标注
- **矩形**: 矩形区域标注
- **圆形**: 圆形区域标注
- **多边形**: 多点多边形标注
- **自由绘制**: 手绘路径标注

### 🎨 样式定制
- 颜色选择器
- 线宽调节（1-10px）
- 线型选择（实线、虚线、点线）
- 填充颜色和透明度控制

### 📁 组织管理
- 分层管理标注
- 标注可见性控制
- 标注锁定功能
- 多选和批量操作

### 📊 数据导入导出
- **Excel (.xlsx)**: 适合数据分析
- **CSV (.csv)**: 通用格式
- **JSON (.json)**: 程序化处理
- 可选包含样式、时间戳和元数据

### 🎯 用户体验
- 撤销/重做功能
- 明暗主题切换
- 响应式设计
- 键盘快捷键支持

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 使用指南

### 1. 创建项目
1. 点击顶部菜单的"项目" → "新建项目"
2. 输入项目名称和描述
3. 点击"创建"

### 2. 添加图像
1. 在左侧边栏的拖拽区域上传图像
2. 或者拖拽图像文件到指定区域
3. 支持的格式：JPEG, PNG, BMP, TIFF

### 3. 开始标注
1. 从工具栏选择标注工具
2. 在图像上绘制标注
3. 在右侧属性面板调整样式和属性

### 4. 管理标注
- 在图层面板查看所有标注
- 点击眼睛图标控制可见性
- 点击锁定图标防止误操作
- 选中标注后在属性面板编辑

### 5. 导出数据
1. 点击顶部菜单的"导出" → "导出标注数据"
2. 选择导出格式和选项
3. 点击"导出"下载文件

## 技术栈

- **前端框架**: React 18 + TypeScript
- **状态管理**: Redux Toolkit
- **UI组件**: 自定义组件 + Lucide React 图标
- **画布渲染**: Konva.js + React-Konva
- **文件处理**: React-Dropzone
- **数据导出**: SheetJS (xlsx) + FileSaver.js
- **构建工具**: Vite

## 项目结构

```
src/
├── components/          # React 组件
│   ├── Canvas/         # 画布相关组件
│   ├── Dialogs/        # 对话框组件
│   └── Layout/         # 布局组件
├── store/              # Redux 状态管理
│   └── slices/         # Redux 切片
├── types/              # TypeScript 类型定义
├── utils/              # 工具函数
└── App.tsx             # 主应用组件
```

## 开发说明

### 添加新的标注类型
1. 在 `types/index.ts` 中扩展 `Marker['type']` 类型
2. 在 `ImageCanvas.tsx` 中添加渲染逻辑
3. 在 `Toolbar.tsx` 中添加工具按钮

### 自定义样式
- 修改 CSS 变量来调整主题颜色
- 在 `MainLayout.css` 中定义明暗主题变量

### 扩展导出格式
- 在 `exportUtils.ts` 中添加新的导出函数
- 在 `ExportDialog.tsx` 中添加格式选项

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
