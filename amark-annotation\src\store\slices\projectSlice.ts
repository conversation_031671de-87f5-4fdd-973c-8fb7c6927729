import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Project, ImageData, <PERSON><PERSON>, MarkerGroup, MarkerStyle, Point } from '../../types';

interface ProjectState {
  currentProject?: Project;
  selectedImageId?: string;
  selectedMarkerIds: string[];
  selectedGroupIds: string[];
  activeToolType: Marker['type'] | 'select';
  currentMarkerStyle: MarkerStyle;
  viewportState: {
    zoom: number;
    pan: Point;
    rotation: number;
  };
}

const defaultMarkerStyle: MarkerStyle = {
  color: '#ff0000',
  lineWidth: 2,
  lineStyle: 'solid',
  fillColor: '#ff000020',
  fillOpacity: 0.2,
  fontSize: 12,
  fontFamily: 'Arial',
  fontWeight: 'normal',
};

const initialState: ProjectState = {
  selectedMarkerIds: [],
  selectedGroupIds: [],
  activeToolType: 'select',
  currentMarkerStyle: defaultMarkerStyle,
  viewportState: {
    zoom: 1,
    pan: { x: 0, y: 0 },
    rotation: 0,
  },
};

const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    createProject: (state, action: PayloadAction<{ name: string; description?: string }>) => {
      const now = new Date();
      state.currentProject = {
        id: crypto.randomUUID(),
        name: action.payload.name,
        description: action.payload.description,
        images: [],
        groups: [],
        settings: {
          defaultMarkerStyle,
          autoSave: true,
          autoSaveInterval: 120,
          maxUndoSteps: 50,
          gridEnabled: false,
          gridSize: 10,
          snapToGrid: false,
          measurementUnit: 'px',
          measurementScale: 1,
        },
        createdAt: now,
        updatedAt: now,
      };
    },

    addImage: (state, action: PayloadAction<{ file: File; url: string; size: { width: number; height: number } }>) => {
      if (!state.currentProject) return;
      
      const now = new Date();
      const newImage: ImageData = {
        id: crypto.randomUUID(),
        name: action.payload.file.name,
        url: action.payload.url,
        originalFile: action.payload.file,
        size: action.payload.size,
        scale: 1,
        rotation: 0,
        position: { x: 0, y: 0 },
        markers: [],
        createdAt: now,
        updatedAt: now,
      };
      
      state.currentProject.images.push(newImage);
      state.selectedImageId = newImage.id;
    },

    selectImage: (state, action: PayloadAction<string>) => {
      state.selectedImageId = action.payload;
      state.selectedMarkerIds = [];
    },

    addMarker: (state, action: PayloadAction<Omit<Marker, 'id' | 'createdAt' | 'updatedAt'>>) => {
      if (!state.currentProject || !state.selectedImageId) return;
      
      const image = state.currentProject.images.find(img => img.id === state.selectedImageId);
      if (!image) return;
      
      const now = new Date();
      const newMarker: Marker = {
        ...action.payload,
        id: crypto.randomUUID(),
        createdAt: now,
        updatedAt: now,
      };
      
      image.markers.push(newMarker);
    },

    updateMarker: (state, action: PayloadAction<{ markerId: string; updates: Partial<Marker> }>) => {
      if (!state.currentProject || !state.selectedImageId) return;
      
      const image = state.currentProject.images.find(img => img.id === state.selectedImageId);
      if (!image) return;
      
      const markerIndex = image.markers.findIndex(m => m.id === action.payload.markerId);
      if (markerIndex === -1) return;
      
      image.markers[markerIndex] = {
        ...image.markers[markerIndex],
        ...action.payload.updates,
        updatedAt: new Date(),
      };
    },

    deleteMarker: (state, action: PayloadAction<string>) => {
      if (!state.currentProject || !state.selectedImageId) return;
      
      const image = state.currentProject.images.find(img => img.id === state.selectedImageId);
      if (!image) return;
      
      image.markers = image.markers.filter(m => m.id !== action.payload);
      state.selectedMarkerIds = state.selectedMarkerIds.filter(id => id !== action.payload);
    },

    selectMarkers: (state, action: PayloadAction<string[]>) => {
      state.selectedMarkerIds = action.payload;
    },

    setActiveToolType: (state, action: PayloadAction<Marker['type'] | 'select'>) => {
      state.activeToolType = action.payload;
    },

    setCurrentMarkerStyle: (state, action: PayloadAction<MarkerStyle>) => {
      state.currentMarkerStyle = action.payload;
    },

    updateViewport: (state, action: PayloadAction<Partial<ProjectState['viewportState']>>) => {
      state.viewportState = { ...state.viewportState, ...action.payload };
    },

    addGroup: (state, action: PayloadAction<{ name: string; color: string; parentId?: string }>) => {
      if (!state.currentProject) return;
      
      const now = new Date();
      const newGroup: MarkerGroup = {
        id: crypto.randomUUID(),
        name: action.payload.name,
        color: action.payload.color,
        visible: true,
        parentId: action.payload.parentId,
        childIds: [],
        markerIds: [],
        createdAt: now,
        updatedAt: now,
      };
      
      state.currentProject.groups.push(newGroup);
      
      // Update parent group if specified
      if (action.payload.parentId) {
        const parentGroup = state.currentProject.groups.find(g => g.id === action.payload.parentId);
        if (parentGroup) {
          parentGroup.childIds.push(newGroup.id);
        }
      }
    },

    updateGroup: (state, action: PayloadAction<{ groupId: string; updates: Partial<MarkerGroup> }>) => {
      if (!state.currentProject) return;
      
      const groupIndex = state.currentProject.groups.findIndex(g => g.id === action.payload.groupId);
      if (groupIndex === -1) return;
      
      state.currentProject.groups[groupIndex] = {
        ...state.currentProject.groups[groupIndex],
        ...action.payload.updates,
        updatedAt: new Date(),
      };
    },

    deleteGroup: (state, action: PayloadAction<string>) => {
      if (!state.currentProject) return;
      
      const group = state.currentProject.groups.find(g => g.id === action.payload);
      if (!group) return;
      
      // Remove from parent's children
      if (group.parentId) {
        const parentGroup = state.currentProject.groups.find(g => g.id === group.parentId);
        if (parentGroup) {
          parentGroup.childIds = parentGroup.childIds.filter(id => id !== action.payload);
        }
      }
      
      // Remove the group
      state.currentProject.groups = state.currentProject.groups.filter(g => g.id !== action.payload);
      state.selectedGroupIds = state.selectedGroupIds.filter(id => id !== action.payload);
    },
  },
});

export const {
  createProject,
  addImage,
  selectImage,
  addMarker,
  updateMarker,
  deleteMarker,
  selectMarkers,
  setActiveToolType,
  setCurrentMarkerStyle,
  updateViewport,
  addGroup,
  updateGroup,
  deleteGroup,
} = projectSlice.actions;

export default projectSlice.reducer;
