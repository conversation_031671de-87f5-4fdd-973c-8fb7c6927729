{"version": 3, "file": "StrokeLineCapProjections.mjs", "sources": ["../../../../../src/util/misc/projectStroke/StrokeLineCapProjections.ts"], "sourcesContent": ["import type { XY } from '../../../Point';\nimport { Point } from '../../../Point';\nimport { getOrthonormalVector, getUnitVector } from '../vectors';\nimport { StrokeLineJoinProjections } from './StrokeLineJoinProjections';\nimport { StrokeProjectionsBase } from './StrokeProjectionsBase';\nimport type { TProjection, TProjectStrokeOnPointsOptions } from './types';\n\n/**\n * class in charge of finding projections for each type of line cap for start/end of an open path\n * @see {@link [Open path projections at #8344](https://github.com/fabricjs/fabric.js/pull/8344#1-open-path)}\n *\n * Reference:\n * - MDN:\n *   - https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineCap\n *   - https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/stroke-linecap\n * - Spec: https://html.spec.whatwg.org/multipage/canvas.html#dom-context-2d-linecap-dev\n * - Playground to understand how the line joins works: https://hypertolosana.github.io/efficient-webgl-stroking/index.html\n * - View the calculated projections for each of the control points: https://codesandbox.io/s/project-stroke-points-with-context-to-trace-b8jc4j?file=/src/index.js\n */\nexport class StrokeLineCapProjections extends StrokeProjectionsBase {\n  /**\n   * edge point\n   */\n  declare A: Point;\n  /**\n   * point next to edge point\n   */\n  declare T: Point;\n\n  constructor(A: XY, T: XY, options: TProjectStrokeOnPointsOptions) {\n    super(options);\n    this.A = new Point(A);\n    this.T = new Point(T);\n  }\n\n  calcOrthogonalProjection(\n    from: Point,\n    to: Point,\n    magnitude: number = this.strokeProjectionMagnitude,\n  ) {\n    const vector = this.createSideVector(from, to);\n    return this.scaleUnitVector(getOrthonormalVector(vector), magnitude);\n  }\n\n  /**\n   * OPEN PATH START/END - Line cap: Butt\n   * Calculation: to find the projections, just find the points orthogonal to the stroke\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#1-1-butt\n   */\n  projectButt() {\n    return [\n      this.projectOrthogonally(this.A, this.T, this.strokeProjectionMagnitude),\n      this.projectOrthogonally(this.A, this.T, -this.strokeProjectionMagnitude),\n    ];\n  }\n\n  /**\n   * OPEN PATH START/END - Line cap: Round\n   * Calculation: same as stroke line join `round`\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#1-2-round\n   */\n  projectRound() {\n    const projections: Point[] = [];\n\n    if (!this.isSkewed() && this.A.eq(this.T)) {\n      /* 1 point case without `skew`\n        When `strokeUniform` is true, scaling has no effect.\n        So we divide by scale, to remove its effect.\n      */\n      const projection = new Point(1, 1)\n        .scalarMultiply(this.strokeProjectionMagnitude)\n        .multiply(this.strokeUniformScalar);\n      projections.push(\n        this.applySkew(this.A.add(projection)),\n        this.applySkew(this.A.subtract(projection)),\n      );\n    } else {\n      projections.push(\n        ...new StrokeLineJoinProjections(\n          this.A,\n          this.T,\n          this.T,\n          this.options,\n        ).projectRound(),\n      );\n    }\n\n    return projections;\n  }\n\n  /**\n   * OPEN PATH START/END - Line cap: Square\n   * Calculation: project a rectangle of points on the stroke in the opposite direction of the vector `AT`\n   *\n   * @see https://github.com/fabricjs/fabric.js/pull/8344#1-3-square\n   */\n  projectSquare() {\n    const projections: Point[] = [];\n\n    if (this.A.eq(this.T)) {\n      /* 1 point case without `skew`\n        When `strokeUniform` is true, scaling has no effect.\n        So we divide by scale, to remove its effect.\n      */\n      const projection = new Point(1, 1)\n        .scalarMultiply(this.strokeProjectionMagnitude)\n        .multiply(this.strokeUniformScalar);\n      projections.push(this.A.add(projection), this.A.subtract(projection));\n    } else {\n      const orthogonalProjection = this.calcOrthogonalProjection(\n        this.A,\n        this.T,\n        this.strokeProjectionMagnitude,\n      );\n      const strokePointingOut = this.scaleUnitVector(\n        getUnitVector(this.createSideVector(this.A, this.T)),\n        -this.strokeProjectionMagnitude,\n      );\n      const projectedA = this.A.add(strokePointingOut);\n      projections.push(\n        projectedA.add(orthogonalProjection),\n        projectedA.subtract(orthogonalProjection),\n      );\n    }\n\n    return projections.map((p) => this.applySkew(p));\n  }\n\n  protected projectPoints() {\n    switch (this.options.strokeLineCap) {\n      case 'round':\n        return this.projectRound();\n      case 'square':\n        return this.projectSquare();\n      default:\n        return this.projectButt();\n    }\n  }\n\n  public project(): TProjection[] {\n    return this.projectPoints().map((point) => ({\n      originPoint: this.A,\n      projectedPoint: point,\n    }));\n  }\n}\n"], "names": ["StrokeLineCapProjections", "StrokeProjectionsBase", "constructor", "A", "T", "options", "Point", "calcOrthogonalProjection", "from", "to", "magnitude", "arguments", "length", "undefined", "strokeProjectionMagnitude", "vector", "createSideVector", "scaleUnitVector", "getOrthonormalVector", "projectButt", "projectOrthogonally", "projectRound", "projections", "isSkewed", "eq", "projection", "scalar<PERSON>ultiply", "multiply", "strokeUniformScalar", "push", "applySkew", "add", "subtract", "StrokeLineJoinProjections", "projectSquare", "orthogonalProjection", "strokePointingOut", "getUnitVector", "projectedA", "map", "p", "projectPoints", "strokeLineCap", "project", "point", "originPoint", "projectedPoint"], "mappings": ";;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMA,wBAAwB,SAASC,qBAAqB,CAAC;AAClE;AACF;AACA;;AAEE;AACF;AACA;;AAGEC,EAAAA,WAAWA,CAACC,CAAK,EAAEC,CAAK,EAAEC,OAAsC,EAAE;IAChE,KAAK,CAACA,OAAO,CAAC,CAAA;AACd,IAAA,IAAI,CAACF,CAAC,GAAG,IAAIG,KAAK,CAACH,CAAC,CAAC,CAAA;AACrB,IAAA,IAAI,CAACC,CAAC,GAAG,IAAIE,KAAK,CAACF,CAAC,CAAC,CAAA;AACvB,GAAA;AAEAG,EAAAA,wBAAwBA,CACtBC,IAAW,EACXC,EAAS,EAET;AAAA,IAAA,IADAC,SAAiB,GAAAC,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAAE,CAAAA,CAAAA,KAAAA,SAAA,GAAAF,SAAA,CAAG,CAAA,CAAA,GAAA,IAAI,CAACG,yBAAyB,CAAA;IAElD,MAAMC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACR,IAAI,EAAEC,EAAE,CAAC,CAAA;IAC9C,OAAO,IAAI,CAACQ,eAAe,CAACC,oBAAoB,CAACH,MAAM,CAAC,EAAEL,SAAS,CAAC,CAAA;AACtE,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACES,EAAAA,WAAWA,GAAG;AACZ,IAAA,OAAO,CACL,IAAI,CAACC,mBAAmB,CAAC,IAAI,CAACjB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACU,yBAAyB,CAAC,EACxE,IAAI,CAACM,mBAAmB,CAAC,IAAI,CAACjB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,CAAC,IAAI,CAACU,yBAAyB,CAAC,CAC1E,CAAA;AACH,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEO,EAAAA,YAAYA,GAAG;IACb,MAAMC,WAAoB,GAAG,EAAE,CAAA;AAE/B,IAAA,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,IAAI,CAACpB,CAAC,CAACqB,EAAE,CAAC,IAAI,CAACpB,CAAC,CAAC,EAAE;AACzC;AACN;AACA;AACA;MACM,MAAMqB,UAAU,GAAG,IAAInB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAC/BoB,cAAc,CAAC,IAAI,CAACZ,yBAAyB,CAAC,CAC9Ca,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAA;AACrCN,MAAAA,WAAW,CAACO,IAAI,CACd,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3B,CAAC,CAAC4B,GAAG,CAACN,UAAU,CAAC,CAAC,EACtC,IAAI,CAACK,SAAS,CAAC,IAAI,CAAC3B,CAAC,CAAC6B,QAAQ,CAACP,UAAU,CAAC,CAC5C,CAAC,CAAA;AACH,KAAC,MAAM;MACLH,WAAW,CAACO,IAAI,CACd,GAAG,IAAII,yBAAyB,CAC9B,IAAI,CAAC9B,CAAC,EACN,IAAI,CAACC,CAAC,EACN,IAAI,CAACA,CAAC,EACN,IAAI,CAACC,OACP,CAAC,CAACgB,YAAY,EAChB,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,OAAOC,WAAW,CAAA;AACpB,GAAA;;AAEA;AACF;AACA;AACA;AACA;AACA;AACEY,EAAAA,aAAaA,GAAG;IACd,MAAMZ,WAAoB,GAAG,EAAE,CAAA;IAE/B,IAAI,IAAI,CAACnB,CAAC,CAACqB,EAAE,CAAC,IAAI,CAACpB,CAAC,CAAC,EAAE;AACrB;AACN;AACA;AACA;MACM,MAAMqB,UAAU,GAAG,IAAInB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAC/BoB,cAAc,CAAC,IAAI,CAACZ,yBAAyB,CAAC,CAC9Ca,QAAQ,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAA;MACrCN,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC1B,CAAC,CAAC4B,GAAG,CAACN,UAAU,CAAC,EAAE,IAAI,CAACtB,CAAC,CAAC6B,QAAQ,CAACP,UAAU,CAAC,CAAC,CAAA;AACvE,KAAC,MAAM;AACL,MAAA,MAAMU,oBAAoB,GAAG,IAAI,CAAC5B,wBAAwB,CACxD,IAAI,CAACJ,CAAC,EACN,IAAI,CAACC,CAAC,EACN,IAAI,CAACU,yBACP,CAAC,CAAA;MACD,MAAMsB,iBAAiB,GAAG,IAAI,CAACnB,eAAe,CAC5CoB,aAAa,CAAC,IAAI,CAACrB,gBAAgB,CAAC,IAAI,CAACb,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC,CAAC,EACpD,CAAC,IAAI,CAACU,yBACR,CAAC,CAAA;MACD,MAAMwB,UAAU,GAAG,IAAI,CAACnC,CAAC,CAAC4B,GAAG,CAACK,iBAAiB,CAAC,CAAA;AAChDd,MAAAA,WAAW,CAACO,IAAI,CACdS,UAAU,CAACP,GAAG,CAACI,oBAAoB,CAAC,EACpCG,UAAU,CAACN,QAAQ,CAACG,oBAAoB,CAC1C,CAAC,CAAA;AACH,KAAA;AAEA,IAAA,OAAOb,WAAW,CAACiB,GAAG,CAAEC,CAAC,IAAK,IAAI,CAACV,SAAS,CAACU,CAAC,CAAC,CAAC,CAAA;AAClD,GAAA;AAEUC,EAAAA,aAAaA,GAAG;AACxB,IAAA,QAAQ,IAAI,CAACpC,OAAO,CAACqC,aAAa;AAChC,MAAA,KAAK,OAAO;AACV,QAAA,OAAO,IAAI,CAACrB,YAAY,EAAE,CAAA;AAC5B,MAAA,KAAK,QAAQ;AACX,QAAA,OAAO,IAAI,CAACa,aAAa,EAAE,CAAA;AAC7B,MAAA;AACE,QAAA,OAAO,IAAI,CAACf,WAAW,EAAE,CAAA;AAC7B,KAAA;AACF,GAAA;AAEOwB,EAAAA,OAAOA,GAAkB;IAC9B,OAAO,IAAI,CAACF,aAAa,EAAE,CAACF,GAAG,CAAEK,KAAK,KAAM;MAC1CC,WAAW,EAAE,IAAI,CAAC1C,CAAC;AACnB2C,MAAAA,cAAc,EAAEF,KAAAA;AAClB,KAAC,CAAC,CAAC,CAAA;AACL,GAAA;AACF;;;;"}