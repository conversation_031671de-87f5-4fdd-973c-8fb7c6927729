import{objectSpread2 as t}from"../../../_virtual/_rollupPluginBabelHelpers.min.mjs";import{reNewline as e}from"../../constants.min.mjs";import{cloneStyles as n}from"../internals/cloneStyles.min.mjs";import{graphemeSplit as l}from"../lang_string.min.mjs";const o=function(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t.fill!==e.fill||t.stroke!==e.stroke||t.strokeWidth!==e.strokeWidth||t.fontSize!==e.fontSize||t.fontFamily!==e.fontFamily||t.fontWeight!==e.fontWeight||t.fontStyle!==e.fontStyle||t.textBackgroundColor!==e.textBackgroundColor||t.deltaY!==e.deltaY||n&&(t.overline!==e.overline||t.underline!==e.underline||t.linethrough!==e.linethrough)},r=(t,e)=>{const r=e.split("\n"),i=[];let s=-1,f={};t=n(t);for(let e=0;e<r.length;e++){const n=l(r[e]);if(t[e])for(let l=0;l<n.length;l++){s++;const n=t[e][l];n&&Object.keys(n).length>0&&(o(f,n,!0)?i.push({start:s,end:s+1,style:n}):i[i.length-1].end++),f=n||{}}else s+=n.length,f={}}return i},i=(o,r)=>{if(!Array.isArray(o))return n(o);const i=r.split(e),s={};let f=-1,m=0;for(let e=0;e<i.length;e++){const n=l(i[e]);for(let l=0;l<n.length;l++)f++,o[m]&&o[m].start<=f&&f<o[m].end&&(s[e]=s[e]||{},s[e][l]=t({},o[m].style),f===o[m].end-1&&m++)}return s};export{o as hasStyleChanged,i as stylesFromArray,r as stylesToArray};
//# sourceMappingURL=textStyles.min.mjs.map
