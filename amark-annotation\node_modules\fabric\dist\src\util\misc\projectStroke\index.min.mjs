import{Point as e}from"../../../Point.min.mjs";import{findIndexRight as n}from"../../internals/findRight.min.mjs";import{StrokeLineCapProjections as t}from"./StrokeLineCapProjections.min.mjs";import{StrokeLineJoinProjections as o}from"./StrokeLineJoinProjections.min.mjs";const i=function(i,r){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const m=[];if(0===i.length)return m;const h=i.reduce(((n,t)=>(n[n.length-1].eq(t)||n.push(new e(t)),n)),[new e(i[0])]);if(1===h.length)s=!0;else if(!s){const e=h[0],t=n(h,(n=>!n.eq(e)));h.splice(t+1)}return h.forEach(((e,n,i)=>{let h,p;0===n?(p=i[1],h=s?e:i[i.length-1]):n===i.length-1?(h=i[n-1],p=s?e:i[0]):(h=i[n-1],p=i[n+1]),s&&1===i.length?m.push(...new t(e,e,r).project()):!s||0!==n&&n!==i.length-1?m.push(...new o(e,h,p,r).project()):m.push(...new t(e,0===n?p:h,r).project())})),m};export{i as projectStrokeOnPoints};
//# sourceMappingURL=index.min.mjs.map
