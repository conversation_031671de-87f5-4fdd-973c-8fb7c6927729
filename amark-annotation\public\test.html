<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AMARK 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2196f3;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            background: #2196f3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 AMARK 图像标注软件</h1>
        
        <div class="status">
            <h3>✅ 服务器状态正常</h3>
            <p>如果您能看到这个页面，说明开发服务器正在正常运行。</p>
        </div>

        <div class="info">
            <h3>📋 测试信息</h3>
            <ul>
                <li><strong>服务器地址:</strong> http://localhost:5173/</li>
                <li><strong>测试页面:</strong> http://localhost:5173/test.html</li>
                <li><strong>主应用:</strong> http://localhost:5173/</li>
                <li><strong>时间:</strong> <span id="current-time"></span></li>
            </ul>
        </div>

        <div class="info">
            <h3>🔧 故障排除</h3>
            <p>如果主应用页面空白，可能的原因：</p>
            <ol>
                <li><strong>JavaScript错误:</strong> 打开浏览器开发者工具(F12)查看控制台错误</li>
                <li><strong>缓存问题:</strong> 按 Ctrl+F5 强制刷新页面</li>
                <li><strong>依赖问题:</strong> 检查npm依赖是否正确安装</li>
                <li><strong>端口冲突:</strong> 尝试使用不同端口</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="button" onclick="window.location.href='/'">
                返回主应用
            </button>
            <button class="button" onclick="location.reload()">
                刷新页面
            </button>
            <button class="button" onclick="window.open('/', '_blank')">
                新窗口打开主应用
            </button>
        </div>

        <div class="info" style="margin-top: 30px;">
            <h3>🚀 功能特性</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>标注工具</h4>
                    <ul>
                        <li>点标记</li>
                        <li>线段</li>
                        <li>矩形</li>
                        <li>圆形</li>
                        <li>多边形</li>
                        <li>自由绘制</li>
                    </ul>
                </div>
                <div>
                    <h4>数据管理</h4>
                    <ul>
                        <li>项目管理</li>
                        <li>图层组织</li>
                        <li>Excel导出</li>
                        <li>CSV导出</li>
                        <li>JSON导出</li>
                        <li>样式定制</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        
        // 每秒更新时间
        setInterval(() => {
            document.getElementById('current-time').textContent = new Date().toLocaleString('zh-CN');
        }, 1000);

        // 检查主应用状态
        function checkMainApp() {
            fetch('/')
                .then(response => {
                    if (response.ok) {
                        console.log('主应用响应正常');
                    } else {
                        console.log('主应用响应异常:', response.status);
                    }
                })
                .catch(error => {
                    console.log('主应用连接失败:', error);
                });
        }

        // 页面加载完成后检查主应用
        checkMainApp();
    </script>
</body>
</html>
