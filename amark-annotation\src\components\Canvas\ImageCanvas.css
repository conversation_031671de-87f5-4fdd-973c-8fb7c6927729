.canvas-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
  background-image: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
    linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  overflow: hidden;
  position: relative;
}

.main-layout.dark .canvas-container {
  background-image: 
    linear-gradient(45deg, #333333 25%, transparent 25%), 
    linear-gradient(-45deg, #333333 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #333333 75%), 
    linear-gradient(-45deg, transparent 75%, #333333 75%);
}

.canvas-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--text-secondary);
  font-size: 16px;
  text-align: center;
}

.canvas-placeholder p {
  margin: 0;
  padding: 40px;
  background-color: var(--bg-secondary);
  border-radius: 8px;
  border: 2px dashed var(--border-color);
}

/* Konva 画布样式 */
.konvajs-content {
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background-color: white;
}

/* 画布控制器 */
.canvas-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  background-color: var(--bg-secondary);
  border-radius: 6px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.canvas-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s;
}

.canvas-control-button:hover {
  background-color: var(--hover-color);
  border-color: var(--accent-color);
}

.canvas-control-button:active {
  background-color: var(--accent-color);
  color: white;
}

/* 缩放信息 */
.zoom-info {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 坐标信息 */
.coordinates-info {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: monospace;
}

/* 工具提示 */
.canvas-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

/* 选择框 */
.selection-box {
  position: absolute;
  border: 2px dashed var(--accent-color);
  background-color: rgba(0, 120, 204, 0.1);
  pointer-events: none;
}

/* 网格 */
.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.3;
}

.grid-line {
  stroke: var(--border-color);
  stroke-width: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-controls {
    top: 8px;
    right: 8px;
    padding: 6px;
  }
  
  .canvas-control-button {
    width: 28px;
    height: 28px;
  }
  
  .zoom-info,
  .coordinates-info {
    bottom: 8px;
    font-size: 11px;
    padding: 4px 8px;
  }
  
  .zoom-info {
    left: 8px;
  }
  
  .coordinates-info {
    right: 8px;
  }
}

/* 加载状态 */
.canvas-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--text-secondary);
}

.canvas-loading .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
