{"version": 3, "file": "toFixed.mjs", "sources": ["../../../../src/util/misc/toFixed.ts"], "sourcesContent": ["/**\n * A wrapper around Number#toFixed, which contrary to native method returns number, not string.\n * @param {number|string} number number to operate on\n * @param {number} fractionDigits number of fraction digits to \"leave\"\n * @return {number}\n */\nexport const toFixed = (number: number | string, fractionDigits: number) =>\n  parseFloat(Number(number).toFixed(fractionDigits));\n"], "names": ["toFixed", "number", "fractionDigits", "parseFloat", "Number"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;MACaA,OAAO,GAAGA,CAACC,MAAuB,EAAEC,cAAsB,KACrEC,UAAU,CAACC,MAAM,CAACH,MAAM,CAAC,CAACD,OAAO,CAACE,cAAc,CAAC;;;;"}