{"version": 3, "file": "StrokeProjectionsBase.min.mjs", "sources": ["../../../../../src/util/misc/projectStroke/StrokeProjectionsBase.ts"], "sourcesContent": ["import type { XY } from '../../../Point';\nimport { Point } from '../../../Point';\nimport { degreesToRadians } from '../radiansDegreesConversion';\nimport { createVector } from '../vectors';\nimport type { TProjectStrokeOnPointsOptions, TProjection } from './types';\n\n/**\n * @see https://github.com/fabricjs/fabric.js/pull/8344\n * @todo consider removing skewing from points before calculating stroke projection,\n * see https://github.com/fabricjs/fabric.js/commit/494a10ee2f8c2278ae9a55b20bf50cf6ee25b064#commitcomment-94751537\n */\nexport abstract class StrokeProjectionsBase {\n  declare options: TProjectStrokeOnPointsOptions;\n  declare scale: Point;\n  declare strokeUniformScalar: Point;\n  declare strokeProjectionMagnitude: number;\n\n  constructor(options: TProjectStrokeOnPointsOptions) {\n    this.options = options;\n    this.strokeProjectionMagnitude = this.options.strokeWidth / 2;\n    this.scale = new Point(this.options.scaleX, this.options.scaleY);\n    this.strokeUniformScalar = this.options.strokeUniform\n      ? new Point(1 / this.options.scaleX, 1 / this.options.scaleY)\n      : new Point(1, 1);\n  }\n\n  /**\n   * When the stroke is uniform, scaling affects the arrangement of points. So we must take it into account.\n   */\n  protected createSideVector(from: XY, to: XY) {\n    const v = createVector(from, to);\n    return this.options.strokeUniform ? v.multiply(this.scale) : v;\n  }\n\n  protected abstract calcOrthogonalProjection(\n    from: Point,\n    to: Point,\n    magnitude?: number,\n  ): Point;\n\n  protected projectOrthogonally(from: Point, to: Point, magnitude?: number) {\n    return this.applySkew(\n      from.add(this.calcOrthogonalProjection(from, to, magnitude)),\n    );\n  }\n\n  protected isSkewed() {\n    return this.options.skewX !== 0 || this.options.skewY !== 0;\n  }\n\n  protected applySkew(point: Point) {\n    const p = new Point(point);\n    // skewY must be applied before skewX as this distortion affects skewX calculation\n    p.y += p.x * Math.tan(degreesToRadians(this.options.skewY));\n    p.x += p.y * Math.tan(degreesToRadians(this.options.skewX));\n    return p;\n  }\n\n  protected scaleUnitVector(unitVector: Point, scalar: number) {\n    return unitVector.multiply(this.strokeUniformScalar).scalarMultiply(scalar);\n  }\n\n  protected abstract projectPoints(): Point[];\n\n  public abstract project(): TProjection[];\n}\n"], "names": ["StrokeProjectionsBase", "constructor", "options", "this", "strokeProjectionMagnitude", "strokeWidth", "scale", "Point", "scaleX", "scaleY", "strokeUniformScalar", "strokeUniform", "createSideVector", "from", "to", "v", "createVector", "multiply", "projectOrthogonally", "magnitude", "applySkew", "add", "calcOrthogonalProjection", "isSkewed", "skewX", "skewY", "point", "p", "y", "x", "Math", "tan", "degreesToRadians", "scaleUnitVector", "unitVector", "scalar", "scalar<PERSON>ultiply"], "mappings": "wKAWO,MAAeA,EAMpBC,WAAAA,CAAYC,GACVC,KAAKD,QAAUA,EACfC,KAAKC,0BAA4BD,KAAKD,QAAQG,YAAc,EAC5DF,KAAKG,MAAQ,IAAIC,EAAMJ,KAAKD,QAAQM,OAAQL,KAAKD,QAAQO,QACzDN,KAAKO,oBAAsBP,KAAKD,QAAQS,cACpC,IAAIJ,EAAM,EAAIJ,KAAKD,QAAQM,OAAQ,EAAIL,KAAKD,QAAQO,QACpD,IAAIF,EAAM,EAAG,EACnB,CAKUK,gBAAAA,CAAiBC,EAAUC,GACnC,MAAMC,EAAIC,EAAaH,EAAMC,GAC7B,OAAOX,KAAKD,QAAQS,cAAgBI,EAAEE,SAASd,KAAKG,OAASS,CAC/D,CAQUG,mBAAAA,CAAoBL,EAAaC,EAAWK,GACpD,OAAOhB,KAAKiB,UACVP,EAAKQ,IAAIlB,KAAKmB,yBAAyBT,EAAMC,EAAIK,IAErD,CAEUI,QAAAA,GACR,OAA8B,IAAvBpB,KAAKD,QAAQsB,OAAsC,IAAvBrB,KAAKD,QAAQuB,KAClD,CAEUL,SAAAA,CAAUM,GAClB,MAAMC,EAAI,IAAIpB,EAAMmB,GAIpB,OAFAC,EAAEC,GAAKD,EAAEE,EAAIC,KAAKC,IAAIC,EAAiB7B,KAAKD,QAAQuB,QACpDE,EAAEE,GAAKF,EAAEC,EAAIE,KAAKC,IAAIC,EAAiB7B,KAAKD,QAAQsB,QAC7CG,CACT,CAEUM,eAAAA,CAAgBC,EAAmBC,GAC3C,OAAOD,EAAWjB,SAASd,KAAKO,qBAAqB0B,eAAeD,EACtE"}