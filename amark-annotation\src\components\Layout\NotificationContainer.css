.notification-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  pointer-events: none;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  pointer-events: auto;
  animation: slideIn 0.3s ease-out;
  transition: all 0.3s ease;
}

.notification:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.notification-success {
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  border-color: rgba(76, 175, 80, 0.3);
}

.notification-error {
  background-color: rgba(244, 67, 54, 0.9);
  color: white;
  border-color: rgba(244, 67, 54, 0.3);
}

.notification-warning {
  background-color: rgba(255, 152, 0, 0.9);
  color: white;
  border-color: rgba(255, 152, 0, 0.3);
}

.notification-info {
  background-color: rgba(33, 150, 243, 0.9);
  color: white;
  border-color: rgba(33, 150, 243, 0.3);
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-message {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
}

.notification-time {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 400;
}

.notification-close {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: inherit;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  margin-top: -2px;
}

.notification-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 动画 */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification.removing {
  animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 深色主题适配 */
.dark .notification {
  backdrop-filter: blur(10px);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark .notification-success {
  background-color: rgba(76, 175, 80, 0.85);
}

.dark .notification-error {
  background-color: rgba(244, 67, 54, 0.85);
}

.dark .notification-warning {
  background-color: rgba(255, 152, 0, 0.85);
}

.dark .notification-info {
  background-color: rgba(33, 150, 243, 0.85);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-container {
    top: 70px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .notification {
    padding: 12px;
    gap: 10px;
  }
  
  .notification-message {
    font-size: 13px;
  }
  
  .notification-time {
    font-size: 11px;
  }
}

/* 多个通知的堆叠效果 */
.notification:nth-child(n+4) {
  opacity: 0.8;
  transform: scale(0.95);
}

.notification:nth-child(n+6) {
  display: none;
}
